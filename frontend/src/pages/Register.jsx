import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  CheckCircle as CheckCircleIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { pricingAPI } from '../services/api';

const Register = () => {
  const { register, isAuthenticated, isLoading, error, clearError } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    userType: 'individual',
    address: {
      street: '',
      city: '',
      state: '',
      pincode: ''
    },
    mandalInfo: {
      organizationName: '',
      establishedYear: '',
      registrationNumber: '',
      presidentName: '',
      secretaryName: '',
      subscriptionPlan: 'free'
    },
    celebrityInfo: {
      profession: ''
    }
  });

  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mandalPlans, setMandalPlans] = useState(null);

  const steps = ['Account Type', 'Basic Info', 'Address', 'Additional Details'];

  const getUserTypeIcon = (type) => {
    switch (type) {
      case 'individual': return <HomeIcon />;
      case 'mandal': return <BusinessIcon />;
      case 'celebrity': return <StarIcon />;
      default: return <PersonAddIcon />;
    }
  };

  const getUserTypeColor = (type) => {
    switch (type) {
      case 'individual': return 'primary';
      case 'mandal': return 'secondary';
      case 'celebrity': return 'warning';
      default: return 'default';
    }
  };

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Fetch mandal pricing plans
  useEffect(() => {
    const fetchMandalPlans = async () => {
      try {
        const response = await pricingAPI.getUserTypePricing('mandal');
        if (response.success) {
          setMandalPlans(response.data.pricing);
        }
      } catch (error) {
        console.error('Error fetching mandal plans:', error);
      }
    };

    if (formData.userType === 'mandal') {
      fetchMandalPlans();
    }
  }, [formData.userType]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateCurrentStep = () => {
    const errors = {};

    switch (activeStep) {
      case 0: // Account Type
        if (!formData.userType) {
          errors.userType = 'Please select an account type';
        }
        break;
      case 1: // Basic Info
        if (!formData.name.trim()) errors.name = 'Name is required';
        if (!formData.email.trim()) errors.email = 'Email is required';
        if (!formData.password) errors.password = 'Password is required';
        if (formData.password.length < 6) errors.password = 'Password must be at least 6 characters';
        if (formData.password !== formData.confirmPassword) {
          errors.confirmPassword = 'Passwords do not match';
        }
        if (!formData.phone.trim()) errors.phone = 'Phone number is required';
        if (!/^[0-9]{10}$/.test(formData.phone)) {
          errors.phone = 'Phone number must be 10 digits';
        }
        break;
      case 2: // Address
        if (!formData.address.street.trim()) errors['address.street'] = 'Street address is required';
        if (!formData.address.city.trim()) errors['address.city'] = 'City is required';
        if (!formData.address.state.trim()) errors['address.state'] = 'State is required';
        if (!formData.address.pincode.trim()) errors['address.pincode'] = 'Pincode is required';
        if (!/^[0-9]{6}$/.test(formData.address.pincode)) {
          errors['address.pincode'] = 'Pincode must be 6 digits';
        }
        break;
      case 3: // Additional Details
        if (formData.userType === 'mandal') {
          if (!formData.mandalInfo.organizationName.trim()) {
            errors['mandalInfo.organizationName'] = 'Organization name is required';
          }
          if (!formData.mandalInfo.establishedYear) {
            errors['mandalInfo.establishedYear'] = 'Established year is required';
          }
          if (!formData.mandalInfo.registrationNumber.trim()) {
            errors['mandalInfo.registrationNumber'] = 'Registration number is required';
          }
          if (!formData.mandalInfo.presidentName.trim()) {
            errors['mandalInfo.presidentName'] = 'President name is required';
          }
          if (!formData.mandalInfo.secretaryName.trim()) {
            errors['mandalInfo.secretaryName'] = 'Secretary name is required';
          }
        } else if (formData.userType === 'celebrity') {
          if (!formData.celebrityInfo.profession.trim()) {
            errors['celebrityInfo.profession'] = 'Profession is required';
          }
        }
        break;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    // Validate all steps
    let allValid = true;
    for (let step = 0; step < steps.length; step++) {
      const currentStep = activeStep;
      setActiveStep(step);
      if (!validateCurrentStep()) {
        allValid = false;
        break;
      }
    }
    setActiveStep(currentStep);
    return allValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await register(formData);

      if (result.success) {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Registration error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return renderAccountTypeStep();
      case 1:
        return renderBasicInfoStep();
      case 2:
        return renderAddressStep();
      case 3:
        return renderAdditionalDetailsStep();
      default:
        return null;
    }
  };

  const renderAccountTypeStep = () => {
    const userTypes = [
      {
        type: 'individual',
        title: 'Individual',
        description: 'Perfect for personal Ganesha celebrations',
        price: 'Free',
        features: ['Upload up to 5 photos', 'Basic contact information', 'Standard support'],
        icon: <HomeIcon sx={{ fontSize: 40 }} />
      },
      {
        type: 'mandal',
        title: 'Mandal Organization',
        description: 'Comprehensive solutions for community organizations',
        price: 'Starting at ₹5,001',
        features: ['Multiple pricing plans', 'Enhanced listings', 'Event management', 'Analytics'],
        icon: <BusinessIcon sx={{ fontSize: 40 }} />,
        popular: true
      },
      {
        type: 'celebrity',
        title: 'Celebrity',
        description: 'Premium showcase for public figures',
        price: 'Free',
        features: ['Verification badge', 'Featured listings', 'Priority support'],
        icon: <StarIcon sx={{ fontSize: 40 }} />
      }
    ];

    return (
      <Box>
        <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
          Choose Your Account Type
        </Typography>
        <Grid container spacing={3}>
          {userTypes.map((type) => (
            <Grid item xs={12} md={4} key={type.type}>
              <Card
                sx={{
                  cursor: 'pointer',
                  border: formData.userType === type.type ? 3 : 1,
                  borderColor: formData.userType === type.type ? 'primary.main' : 'divider',
                  transform: type.popular ? 'scale(1.05)' : 'none',
                  position: 'relative',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: type.popular ? 'scale(1.08)' : 'scale(1.03)',
                    boxShadow: 4,
                  },
                }}
                onClick={() => handleInputChange({ target: { name: 'userType', value: type.type } })}
              >
                {type.popular && (
                  <Chip
                    label="Most Popular"
                    color="primary"
                    sx={{
                      position: 'absolute',
                      top: -12,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      fontWeight: 600,
                      zIndex: 1,
                    }}
                  />
                )}
                <CardContent sx={{ textAlign: 'center', pt: type.popular ? 4 : 3 }}>
                  <Box sx={{ color: getUserTypeColor(type.type) + '.main', mb: 2 }}>
                    {type.icon}
                  </Box>
                  <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                    {type.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {type.description}
                  </Typography>
                  <Typography variant="h6" color="primary.main" sx={{ mb: 2, fontWeight: 700 }}>
                    {type.price}
                  </Typography>
                  <List dense>
                    {type.features.map((feature, idx) => (
                      <ListItem key={idx} sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 30 }}>
                          <CheckCircleIcon sx={{ color: 'success.main', fontSize: 16 }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={feature}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  const renderBasicInfoStep = () => (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
        Basic Information
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Full Name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            error={!!formErrors.name}
            helperText={formErrors.name}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Email Address"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            error={!!formErrors.email}
            helperText={formErrors.email}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Phone Number"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            error={!!formErrors.phone}
            helperText={formErrors.phone}
            placeholder="10-digit number"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleInputChange}
            error={!!formErrors.password}
            helperText={formErrors.password}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Confirm Password"
            name="confirmPassword"
            type="password"
            value={formData.confirmPassword}
            onChange={handleInputChange}
            error={!!formErrors.confirmPassword}
            helperText={formErrors.confirmPassword}
            required
          />
        </Grid>
      </Grid>
    </Box>
  );

  const renderAddressStep = () => (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
        Address Information
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Street Address"
            name="address.street"
            value={formData.address.street}
            onChange={handleInputChange}
            error={!!formErrors['address.street']}
            helperText={formErrors['address.street']}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="City"
            name="address.city"
            value={formData.address.city}
            onChange={handleInputChange}
            error={!!formErrors['address.city']}
            helperText={formErrors['address.city']}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="State"
            name="address.state"
            value={formData.address.state}
            onChange={handleInputChange}
            error={!!formErrors['address.state']}
            helperText={formErrors['address.state']}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Pincode"
            name="address.pincode"
            value={formData.address.pincode}
            onChange={handleInputChange}
            error={!!formErrors['address.pincode']}
            helperText={formErrors['address.pincode']}
            placeholder="6-digit code"
            required
          />
        </Grid>
      </Grid>
    </Box>
  );

  const renderAdditionalDetailsStep = () => {
    switch (formData.userType) {
      case 'mandal':
        return renderMandalDetails();
      case 'celebrity':
        return renderCelebrityDetails();
      default:
        return renderIndividualDetails();
    }
  };

  const renderMandalDetails = () => (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
        Mandal Information
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Organization Name"
            name="mandalInfo.organizationName"
            value={formData.mandalInfo.organizationName}
            onChange={handleInputChange}
            error={!!formErrors['mandalInfo.organizationName']}
            helperText={formErrors['mandalInfo.organizationName']}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Established Year"
            name="mandalInfo.establishedYear"
            type="number"
            value={formData.mandalInfo.establishedYear}
            onChange={handleInputChange}
            error={!!formErrors['mandalInfo.establishedYear']}
            helperText={formErrors['mandalInfo.establishedYear']}
            inputProps={{ min: 1800, max: new Date().getFullYear() }}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Registration Number"
            name="mandalInfo.registrationNumber"
            value={formData.mandalInfo.registrationNumber}
            onChange={handleInputChange}
            error={!!formErrors['mandalInfo.registrationNumber']}
            helperText={formErrors['mandalInfo.registrationNumber']}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="President Name"
            name="mandalInfo.presidentName"
            value={formData.mandalInfo.presidentName}
            onChange={handleInputChange}
            error={!!formErrors['mandalInfo.presidentName']}
            helperText={formErrors['mandalInfo.presidentName']}
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Secretary Name"
            name="mandalInfo.secretaryName"
            value={formData.mandalInfo.secretaryName}
            onChange={handleInputChange}
            error={!!formErrors['mandalInfo.secretaryName']}
            helperText={formErrors['mandalInfo.secretaryName']}
            required
          />
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <InputLabel>Subscription Plan</InputLabel>
            <Select
              name="mandalInfo.subscriptionPlan"
              value={formData.mandalInfo.subscriptionPlan}
              onChange={handleInputChange}
              label="Subscription Plan"
            >
              {mandalPlans && Object.keys(mandalPlans).map(planKey => (
                <MenuItem key={planKey} value={planKey}>
                  {mandalPlans[planKey].plan.toUpperCase()} - ₹{mandalPlans[planKey].price}
                  {planKey === 'free' ? ' (Free)' : '/year'}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {mandalPlans && formData.mandalInfo.subscriptionPlan && (
            <Card sx={{ mt: 2, bgcolor: 'background.default' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Plan Features:
                </Typography>
                <List dense>
                  {mandalPlans[formData.mandalInfo.subscriptionPlan].features.map((feature, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CheckCircleIcon color="success" />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
  );

  const renderCelebrityDetails = () => (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
        Celebrity Information
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Profession"
            name="celebrityInfo.profession"
            value={formData.celebrityInfo.profession}
            onChange={handleInputChange}
            error={!!formErrors['celebrityInfo.profession']}
            helperText={formErrors['celebrityInfo.profession']}
            placeholder="e.g., Actor, Singer, Politician"
            required
          />
        </Grid>
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mt: 2 }}>
            📝 Celebrity accounts require verification. You'll receive a verification badge once approved.
          </Alert>
        </Grid>
      </Grid>
    </Box>
  );

  const renderIndividualDetails = () => (
    <Box>
      <Typography variant="h5" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
        All Set!
      </Typography>
      <Alert severity="success" sx={{ textAlign: 'center' }}>
        🏠 Individual accounts are perfect for personal Ganesha celebrations and home decorations.
        You're ready to create your account!
      </Alert>
    </Box>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
        py: 4,
      }}
    >
      <Container maxWidth="md">
        <Paper
          elevation={10}
          sx={{
            p: { xs: 3, sm: 4 },
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* Header */}
          <Box textAlign="center" mb={4}>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
                color: 'white',
                mb: 2,
              }}
            >
              <PersonAddIcon sx={{ fontSize: 40 }} />
            </Box>
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
              Join Live Ganesh
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Create your account to showcase your Ganesha celebrations
            </Typography>
          </Box>

          {/* Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }} alternativeLabel={!isMobile}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Step Content */}
          <Box sx={{ mb: 4 }}>
            {renderStepContent(activeStep)}
          </Box>

          {/* Error Display */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Navigation Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{ mr: 1 }}
            >
              Back
            </Button>

            <Box sx={{ flex: '1 1 auto' }} />

            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={isSubmitting}
                size="large"
                sx={{
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #e55a2b, #d67d1a)',
                  },
                }}
              >
                {isSubmitting ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
                    Creating Account...
                  </>
                ) : (
                  'Create Account'
                )}
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                size="large"
                sx={{
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #e55a2b, #d67d1a)',
                  },
                }}
              >
                Next
              </Button>
            )}
          </Box>

          <Divider sx={{ my: 3 }} />

          <Box textAlign="center">
            <Typography variant="body2" color="text.secondary">
              Already have an account?{' '}
              <Link
                to="/login"
                style={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                }}
              >
                Sign in here
              </Link>
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default Register;
