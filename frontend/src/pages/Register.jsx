import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { pricingAPI } from '../services/api';
import './Register.css';

const Register = () => {
  const { register, isAuthenticated, isLoading, error, clearError } = useAuth();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    userType: 'individual',
    address: {
      street: '',
      city: '',
      state: '',
      pincode: ''
    },
    mandalInfo: {
      organizationName: '',
      establishedYear: '',
      registrationNumber: '',
      presidentName: '',
      secretaryName: '',
      subscriptionPlan: 'free'
    },
    celebrityInfo: {
      profession: ''
    }
  });

  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mandalPlans, setMandalPlans] = useState(null);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Fetch mandal pricing plans
  useEffect(() => {
    const fetchMandalPlans = async () => {
      try {
        const response = await pricingAPI.getUserTypePricing('mandal');
        if (response.success) {
          setMandalPlans(response.data.pricing);
        }
      } catch (error) {
        console.error('Error fetching mandal plans:', error);
      }
    };

    if (formData.userType === 'mandal') {
      fetchMandalPlans();
    }
  }, [formData.userType]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    // Basic validation
    if (!formData.name.trim()) errors.name = 'Name is required';
    if (!formData.email.trim()) errors.email = 'Email is required';
    if (!formData.password) errors.password = 'Password is required';
    if (formData.password.length < 6) errors.password = 'Password must be at least 6 characters';
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    if (!formData.phone.trim()) errors.phone = 'Phone number is required';
    if (!/^[0-9]{10}$/.test(formData.phone)) {
      errors.phone = 'Phone number must be 10 digits';
    }

    // Address validation
    if (!formData.address.street.trim()) errors['address.street'] = 'Street address is required';
    if (!formData.address.city.trim()) errors['address.city'] = 'City is required';
    if (!formData.address.state.trim()) errors['address.state'] = 'State is required';
    if (!formData.address.pincode.trim()) errors['address.pincode'] = 'Pincode is required';
    if (!/^[0-9]{6}$/.test(formData.address.pincode)) {
      errors['address.pincode'] = 'Pincode must be 6 digits';
    }

    // Mandal-specific validation
    if (formData.userType === 'mandal') {
      if (!formData.mandalInfo.organizationName.trim()) {
        errors['mandalInfo.organizationName'] = 'Organization name is required';
      }
      if (!formData.mandalInfo.establishedYear) {
        errors['mandalInfo.establishedYear'] = 'Established year is required';
      }
      if (!formData.mandalInfo.registrationNumber.trim()) {
        errors['mandalInfo.registrationNumber'] = 'Registration number is required';
      }
      if (!formData.mandalInfo.presidentName.trim()) {
        errors['mandalInfo.presidentName'] = 'President name is required';
      }
      if (!formData.mandalInfo.secretaryName.trim()) {
        errors['mandalInfo.secretaryName'] = 'Secretary name is required';
      }
    }

    // Celebrity-specific validation
    if (formData.userType === 'celebrity') {
      if (!formData.celebrityInfo.profession.trim()) {
        errors['celebrityInfo.profession'] = 'Profession is required';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await register(formData);
      
      if (result.success) {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Registration error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderUserTypeSpecificFields = () => {
    switch (formData.userType) {
      case 'mandal':
        return (
          <div className="mandal-fields">
            <h3>Mandal Information</h3>
            
            <div className="form-group">
              <label htmlFor="organizationName">Organization Name *</label>
              <input
                type="text"
                id="organizationName"
                name="mandalInfo.organizationName"
                value={formData.mandalInfo.organizationName}
                onChange={handleInputChange}
                className={formErrors['mandalInfo.organizationName'] ? 'error' : ''}
              />
              {formErrors['mandalInfo.organizationName'] && (
                <span className="error-message">{formErrors['mandalInfo.organizationName']}</span>
              )}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="establishedYear">Established Year *</label>
                <input
                  type="number"
                  id="establishedYear"
                  name="mandalInfo.establishedYear"
                  value={formData.mandalInfo.establishedYear}
                  onChange={handleInputChange}
                  min="1800"
                  max={new Date().getFullYear()}
                  className={formErrors['mandalInfo.establishedYear'] ? 'error' : ''}
                />
                {formErrors['mandalInfo.establishedYear'] && (
                  <span className="error-message">{formErrors['mandalInfo.establishedYear']}</span>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="registrationNumber">Registration Number *</label>
                <input
                  type="text"
                  id="registrationNumber"
                  name="mandalInfo.registrationNumber"
                  value={formData.mandalInfo.registrationNumber}
                  onChange={handleInputChange}
                  className={formErrors['mandalInfo.registrationNumber'] ? 'error' : ''}
                />
                {formErrors['mandalInfo.registrationNumber'] && (
                  <span className="error-message">{formErrors['mandalInfo.registrationNumber']}</span>
                )}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="presidentName">President Name *</label>
                <input
                  type="text"
                  id="presidentName"
                  name="mandalInfo.presidentName"
                  value={formData.mandalInfo.presidentName}
                  onChange={handleInputChange}
                  className={formErrors['mandalInfo.presidentName'] ? 'error' : ''}
                />
                {formErrors['mandalInfo.presidentName'] && (
                  <span className="error-message">{formErrors['mandalInfo.presidentName']}</span>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="secretaryName">Secretary Name *</label>
                <input
                  type="text"
                  id="secretaryName"
                  name="mandalInfo.secretaryName"
                  value={formData.mandalInfo.secretaryName}
                  onChange={handleInputChange}
                  className={formErrors['mandalInfo.secretaryName'] ? 'error' : ''}
                />
                {formErrors['mandalInfo.secretaryName'] && (
                  <span className="error-message">{formErrors['mandalInfo.secretaryName']}</span>
                )}
              </div>
            </div>

            {/* Subscription Plan Selection */}
            <div className="form-group">
              <label htmlFor="subscriptionPlan">Subscription Plan *</label>
              <select
                id="subscriptionPlan"
                name="mandalInfo.subscriptionPlan"
                value={formData.mandalInfo.subscriptionPlan}
                onChange={handleInputChange}
              >
                {mandalPlans && Object.keys(mandalPlans).map(planKey => (
                  <option key={planKey} value={planKey}>
                    {mandalPlans[planKey].plan.toUpperCase()} - ₹{mandalPlans[planKey].price}
                    {planKey === 'free' ? ' (Free)' : '/year'}
                  </option>
                ))}
              </select>
              
              {mandalPlans && formData.mandalInfo.subscriptionPlan && (
                <div className="plan-details">
                  <h4>Plan Features:</h4>
                  <ul>
                    {mandalPlans[formData.mandalInfo.subscriptionPlan].features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        );

      case 'celebrity':
        return (
          <div className="celebrity-fields">
            <h3>Celebrity Information</h3>
            
            <div className="form-group">
              <label htmlFor="profession">Profession *</label>
              <input
                type="text"
                id="profession"
                name="celebrityInfo.profession"
                value={formData.celebrityInfo.profession}
                onChange={handleInputChange}
                placeholder="e.g., Actor, Singer, Politician"
                className={formErrors['celebrityInfo.profession'] ? 'error' : ''}
              />
              {formErrors['celebrityInfo.profession'] && (
                <span className="error-message">{formErrors['celebrityInfo.profession']}</span>
              )}
            </div>
            
            <div className="info-note">
              <p>📝 Celebrity accounts require verification. You'll receive a verification badge once approved.</p>
            </div>
          </div>
        );

      default:
        return (
          <div className="individual-info">
            <div className="info-note">
              <p>🏠 Individual accounts are perfect for personal Ganesha celebrations and home decorations.</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="register-page">
      <div className="register-container">
        <div className="register-header">
          <h1>Join Live Ganesh</h1>
          <p>Create your account to showcase your Ganesha celebrations</p>
        </div>

        <form onSubmit={handleSubmit} className="register-form">
          {/* User Type Selection */}
          <div className="form-group">
            <label htmlFor="userType">Account Type *</label>
            <select
              id="userType"
              name="userType"
              value={formData.userType}
              onChange={handleInputChange}
              className="user-type-select"
            >
              <option value="individual">Individual (Free)</option>
              <option value="mandal">Mandal (Multiple Plans)</option>
              <option value="celebrity">Celebrity (Free)</option>
            </select>
          </div>

          {/* Basic Information */}
          <div className="form-section">
            <h3>Basic Information</h3>
            
            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={formErrors.name ? 'error' : ''}
              />
              {formErrors.name && <span className="error-message">{formErrors.name}</span>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="email">Email Address *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={formErrors.email ? 'error' : ''}
                />
                {formErrors.email && <span className="error-message">{formErrors.email}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="phone">Phone Number *</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="10-digit number"
                  className={formErrors.phone ? 'error' : ''}
                />
                {formErrors.phone && <span className="error-message">{formErrors.phone}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="password">Password *</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={formErrors.password ? 'error' : ''}
                />
                {formErrors.password && <span className="error-message">{formErrors.password}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirm Password *</label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={formErrors.confirmPassword ? 'error' : ''}
                />
                {formErrors.confirmPassword && <span className="error-message">{formErrors.confirmPassword}</span>}
              </div>
            </div>
          </div>

          {/* Address Information */}
          <div className="form-section">
            <h3>Address Information</h3>
            
            <div className="form-group">
              <label htmlFor="street">Street Address *</label>
              <input
                type="text"
                id="street"
                name="address.street"
                value={formData.address.street}
                onChange={handleInputChange}
                className={formErrors['address.street'] ? 'error' : ''}
              />
              {formErrors['address.street'] && <span className="error-message">{formErrors['address.street']}</span>}
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="city">City *</label>
                <input
                  type="text"
                  id="city"
                  name="address.city"
                  value={formData.address.city}
                  onChange={handleInputChange}
                  className={formErrors['address.city'] ? 'error' : ''}
                />
                {formErrors['address.city'] && <span className="error-message">{formErrors['address.city']}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="state">State *</label>
                <input
                  type="text"
                  id="state"
                  name="address.state"
                  value={formData.address.state}
                  onChange={handleInputChange}
                  className={formErrors['address.state'] ? 'error' : ''}
                />
                {formErrors['address.state'] && <span className="error-message">{formErrors['address.state']}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="pincode">Pincode *</label>
                <input
                  type="text"
                  id="pincode"
                  name="address.pincode"
                  value={formData.address.pincode}
                  onChange={handleInputChange}
                  placeholder="6-digit code"
                  className={formErrors['address.pincode'] ? 'error' : ''}
                />
                {formErrors['address.pincode'] && <span className="error-message">{formErrors['address.pincode']}</span>}
              </div>
            </div>
          </div>

          {/* User Type Specific Fields */}
          {renderUserTypeSpecificFields()}

          {/* Error Display */}
          {error && (
            <div className="error-alert">
              <p>{error}</p>
            </div>
          )}

          {/* Submit Button */}
          <button 
            type="submit" 
            className="register-btn"
            disabled={isSubmitting || isLoading}
          >
            {isSubmitting ? 'Creating Account...' : 'Create Account'}
          </button>
        </form>

        <div className="register-footer">
          <p>
            Already have an account? <Link to="/login">Sign in here</Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
