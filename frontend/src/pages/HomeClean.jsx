import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardMedia,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Chip,
  Paper,
  Menu,
  MenuItem,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Search as SearchIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated, user } = useAuth() || {};
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // State for tabs and filters
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [cityAnchor, setCityAnchor] = useState(null);
  const [heightAnchor, setHeightAnchor] = useState(null);
  const [daysAnchor, setDaysAnchor] = useState(null);
  const [selectedCity, setSelectedCity] = useState('City');
  const [selectedHeight, setSelectedHeight] = useState('Height');
  const [selectedDays, setSelectedDays] = useState('Days');

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  // Sample listings data
  const listings = [
    {
      id: 1,
      title: "Eco-Friendly Ganesh",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
      category: "home"
    },
    {
      id: 2,
      title: "Traditional Ganesh",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400",
      category: "home"
    },
    {
      id: 3,
      title: "Modern Ganesh",
      image: "https://images.unsplash.com/photo-1582510003544-4d00b7f74220?w=400",
      category: "mandal"
    },
    {
      id: 4,
      title: "Artistic Ganesh",
      image: "https://images.unsplash.com/photo-1580477667995-2b94f01c9516?w=400",
      category: "mandal"
    },
    {
      id: 5,
      title: "Miniature Ganesh",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
      category: "celebrity"
    },
    {
      id: 6,
      title: "Themed Ganesh",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400",
      category: "celebrity"
    },
    {
      id: 7,
      title: "Another Ganesh",
      image: "https://images.unsplash.com/photo-1582510003544-4d00b7f74220?w=400",
      category: "home"
    },
    {
      id: 8,
      title: "Yet Another Ganesh",
      image: "https://images.unsplash.com/photo-1580477667995-2b94f01c9516?w=400",
      category: "mandal"
    }
  ];

  const categories = ['home', 'mandal', 'celebrity'];
  const currentCategory = categories[selectedTab];
  const filteredListings = listings.filter(listing => listing.category === currentCategory);

  const cities = ['Mumbai', 'Pune', 'Delhi', 'Bangalore', 'Chennai'];
  const heights = ['1-2 feet', '2-4 feet', '4-6 feet', '6+ feet'];
  const days = ['1 Day', '3 Days', '5 Days', '7 Days', '11 Days'];

  return (
    <Box sx={{ bgcolor: 'white', minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #a4243b 0%, #d8973c 50%, #bd632f 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: '2.5rem', md: '3.5rem' },
              fontWeight: 700,
              mb: 3,
              lineHeight: 1.2,
            }}
          >
            Welcome to Ganesh Darshan
          </Typography>
          <Typography
            variant="h5"
            sx={{
              fontSize: { xs: '1.1rem', md: '1.3rem' },
              mb: 4,
              opacity: 0.9,
              fontWeight: 400,
              maxWidth: 600,
              mx: 'auto',
            }}
          >
            Discover beautiful Ganesh celebrations from homes, mandals, and celebrities across India
          </Typography>
          <Button
            variant="contained"
            size="large"
            sx={{
              bgcolor: 'white',
              color: 'primary.main',
              px: 6,
              py: 2,
              fontSize: '1.1rem',
              fontWeight: 600,
              borderRadius: 2,
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.95)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            Explore Celebrations
          </Button>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ py: 6 }}>
        {/* Search and Filters */}
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 6,
            borderRadius: 3,
            border: '1px solid',
            borderColor: 'grey.200',
            bgcolor: 'grey.50',
          }}
        >
          {/* Search Bar */}
          <TextField
            fullWidth
            placeholder="Search for Ganesh celebrations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            sx={{
              mb: 3,
              '& .MuiOutlinedInput-root': {
                bgcolor: 'white',
                borderRadius: 2,
                '& fieldset': {
                  borderColor: 'grey.300',
                },
                '&:hover fieldset': {
                  borderColor: 'primary.main',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: 'grey.500' }} />
                </InputAdornment>
              ),
            }}
          />

          {/* Filter Chips */}
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip
              label={selectedCity}
              deleteIcon={<KeyboardArrowDownIcon />}
              onDelete={(e) => setCityAnchor(e.currentTarget)}
              onClick={(e) => setCityAnchor(e.currentTarget)}
              variant="outlined"
              sx={{
                bgcolor: 'white',
                borderColor: 'grey.300',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
            />
            <Chip
              label={selectedHeight}
              deleteIcon={<KeyboardArrowDownIcon />}
              onDelete={(e) => setHeightAnchor(e.currentTarget)}
              onClick={(e) => setHeightAnchor(e.currentTarget)}
              variant="outlined"
              sx={{
                bgcolor: 'white',
                borderColor: 'grey.300',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
            />
            <Chip
              label={selectedDays}
              deleteIcon={<KeyboardArrowDownIcon />}
              onDelete={(e) => setDaysAnchor(e.currentTarget)}
              onClick={(e) => setDaysAnchor(e.currentTarget)}
              variant="outlined"
              sx={{
                bgcolor: 'white',
                borderColor: 'grey.300',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
            />
          </Box>
        </Paper>

        {/* Filter Menus */}
        <Menu
          anchorEl={cityAnchor}
          open={Boolean(cityAnchor)}
          onClose={() => setCityAnchor(null)}
        >
          {cities.map((city) => (
            <MenuItem
              key={city}
              onClick={() => {
                setSelectedCity(city);
                setCityAnchor(null);
              }}
            >
              {city}
            </MenuItem>
          ))}
        </Menu>

        <Menu
          anchorEl={heightAnchor}
          open={Boolean(heightAnchor)}
          onClose={() => setHeightAnchor(null)}
        >
          {heights.map((height) => (
            <MenuItem
              key={height}
              onClick={() => {
                setSelectedHeight(height);
                setHeightAnchor(null);
              }}
            >
              {height}
            </MenuItem>
          ))}
        </Menu>

        <Menu
          anchorEl={daysAnchor}
          open={Boolean(daysAnchor)}
          onClose={() => setDaysAnchor(null)}
        >
          {days.map((day) => (
            <MenuItem
              key={day}
              onClick={() => {
                setSelectedDays(day);
                setDaysAnchor(null);
              }}
            >
              {day}
            </MenuItem>
          ))}
        </Menu>

        {/* Category Tabs */}
        <Box sx={{ mb: 4 }}>
          <Tabs
            value={selectedTab}
            onChange={handleTabChange}
            centered
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
                minWidth: 120,
                color: 'text.secondary',
                '&.Mui-selected': {
                  color: 'primary.main',
                },
              },
              '& .MuiTabs-indicator': {
                backgroundColor: 'primary.main',
                height: 3,
              },
            }}
          >
            <Tab label="Home Ganesh" />
            <Tab label="Mandal Ganesh" />
            <Tab label="Celebrity Ganesh" />
          </Tabs>
        </Box>

        {/* Listings Grid */}
        <Grid container spacing={4} sx={{ mb: 8 }}>
          {filteredListings.map((listing) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={listing.id}>
              <Card
                component={Link}
                to={`/listing/${listing.id}`}
                elevation={0}
                sx={{
                  textDecoration: 'none',
                  borderRadius: 3,
                  overflow: 'hidden',
                  border: '1px solid',
                  borderColor: 'grey.200',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(0,0,0,0.1)',
                    borderColor: 'primary.main',
                  },
                }}
              >
                <CardMedia
                  component="img"
                  height="240"
                  image={listing.image}
                  alt={listing.title}
                  sx={{ objectFit: 'cover' }}
                />
                <Box sx={{ p: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: 'text.primary',
                      fontSize: '1rem',
                      lineHeight: 1.4,
                    }}
                  >
                    {listing.title}
                  </Typography>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* View All Button */}
        <Box sx={{ textAlign: 'center', mb: 8 }}>
          <Button
            variant="outlined"
            size="large"
            sx={{
              px: 6,
              py: 2,
              fontSize: '1rem',
              fontWeight: 600,
              borderRadius: 2,
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.main',
                color: 'white',
              },
            }}
          >
            View All Celebrations
          </Button>
        </Box>

        {/* Features Section */}
        <Paper
          elevation={0}
          sx={{
            p: 6,
            borderRadius: 3,
            bgcolor: 'grey.50',
            textAlign: 'center',
            mb: 8,
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 2,
              color: 'text.primary',
            }}
          >
            Why Choose Ganesh Darshan?
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: 'text.secondary',
              mb: 4,
              maxWidth: 600,
              mx: 'auto',
              fontSize: '1.1rem',
              lineHeight: 1.6,
            }}
          >
            Connect with millions of devotees, discover beautiful celebrations, and share your own Ganesh festivities with our growing community.
          </Typography>

          <Grid container spacing={4} sx={{ mt: 2 }}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                  10K+
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Active Users
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                  500+
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Mandals
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                  50K+
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Photos Shared
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* CTA Section */}
        <Box
          sx={{
            textAlign: 'center',
            py: 8,
            px: 4,
            borderRadius: 3,
            background: 'linear-gradient(135deg, #a4243b 0%, #d8973c 50%, #bd632f 100%)',
            color: 'white',
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 2,
            }}
          >
            Ready to Share Your Celebration?
          </Typography>
          <Typography
            variant="body1"
            sx={{
              mb: 4,
              opacity: 0.9,
              maxWidth: 500,
              mx: 'auto',
              fontSize: '1.1rem',
            }}
          >
            Join thousands of devotees sharing their Ganesh celebrations. Create your listing today!
          </Typography>
          <Button
            component={Link}
            to="/register"
            variant="contained"
            size="large"
            sx={{
              bgcolor: 'white',
              color: 'primary.main',
              px: 6,
              py: 2,
              fontSize: '1.1rem',
              fontWeight: 600,
              borderRadius: 2,
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.95)',
                transform: 'translateY(-2px)',
              },
            }}
          >
            Get Started
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default Home;
