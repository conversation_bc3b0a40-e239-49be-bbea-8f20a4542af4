import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { pricingAPI } from '../services/api';
import { useAuth } from '../context/AuthContext';
import './Pricing.css';

const Pricing = () => {
  const { isAuthenticated } = useAuth();
  const [pricingData, setPricingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPricing = async () => {
      try {
        const response = await pricingAPI.getAllPricing();
        if (response.success) {
          setPricingData(response.data.pricing);
        }
      } catch (err) {
        setError('Failed to load pricing information');
        console.error('Error fetching pricing:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPricing();
  }, []);

  if (loading) {
    return (
      <div className="pricing-page">
        <div className="loading-container">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <p>Loading pricing information...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pricing-page">
        <div className="error-container">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="pricing-page">
      <div className="pricing-container">
        <div className="pricing-header">
          <h1>Choose Your Plan</h1>
          <p>Select the perfect plan for your Ganesha celebration needs</p>
        </div>

        {/* Individual & Celebrity (Free) */}
        <div className="free-plans">
          <h2>Free Plans</h2>
          <div className="plans-grid">
            {/* Individual Plan */}
            <div className="plan-card">
              <div className="plan-header">
                <h3>Individual</h3>
                <div className="plan-price">
                  <span className="price">Free</span>
                  <span className="period">Forever</span>
                </div>
              </div>
              
              <div className="plan-features">
                <h4>Perfect for:</h4>
                <p>Personal and family Ganesha celebrations</p>
                
                <h4>Features:</h4>
                <ul>
                  {pricingData?.individual?.features?.map((feature, index) => (
                    <li key={index}>✅ {feature}</li>
                  ))}
                </ul>
              </div>
              
              <div className="plan-action">
                {isAuthenticated ? (
                  <Link to="/dashboard" className="btn btn-outline">
                    Go to Dashboard
                  </Link>
                ) : (
                  <Link to="/register" className="btn btn-outline">
                    Get Started
                  </Link>
                )}
              </div>
            </div>

            {/* Celebrity Plan */}
            <div className="plan-card featured">
              <div className="plan-badge">Verification Required</div>
              <div className="plan-header">
                <h3>Celebrity</h3>
                <div className="plan-price">
                  <span className="price">Free</span>
                  <span className="period">With Verification</span>
                </div>
              </div>
              
              <div className="plan-features">
                <h4>Perfect for:</h4>
                <p>Public figures, actors, politicians, and celebrities</p>
                
                <h4>Features:</h4>
                <ul>
                  {pricingData?.celebrity?.features?.map((feature, index) => (
                    <li key={index}>✅ {feature}</li>
                  ))}
                </ul>
              </div>
              
              <div className="plan-action">
                {isAuthenticated ? (
                  <Link to="/dashboard" className="btn btn-primary">
                    Go to Dashboard
                  </Link>
                ) : (
                  <Link to="/register" className="btn btn-primary">
                    Apply for Verification
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Mandal Plans */}
        <div className="mandal-plans">
          <h2>Mandal Plans</h2>
          <p className="mandal-subtitle">Comprehensive solutions for community organizations</p>
          
          <div className="plans-grid mandal-grid">
            {pricingData?.mandal && Object.entries(pricingData.mandal).map(([planKey, plan]) => (
              <div 
                key={planKey} 
                className={`plan-card ${planKey === 'platinum' ? 'featured' : ''}`}
              >
                {planKey === 'platinum' && <div className="plan-badge">Most Popular</div>}
                
                <div className="plan-header">
                  <h3>{plan.plan.toUpperCase()}</h3>
                  <div className="plan-price">
                    <span className="price">
                      {plan.price === 0 ? 'Free' : `₹${plan.price.toLocaleString()}`}
                    </span>
                    {plan.price > 0 && <span className="period">/year</span>}
                  </div>
                </div>
                
                <div className="plan-features">
                  <h4>Features:</h4>
                  <ul>
                    {plan.features.map((feature, index) => (
                      <li key={index}>✅ {feature}</li>
                    ))}
                  </ul>
                  
                  <h4>Limits:</h4>
                  <div className="plan-limits">
                    <div className="limit-item">
                      <span>Photos: </span>
                      <span>{plan.limitations.maxPhotos === -1 ? 'Unlimited' : plan.limitations.maxPhotos}</span>
                    </div>
                    <div className="limit-item">
                      <span>Videos: </span>
                      <span>{plan.limitations.maxVideos === -1 ? 'Unlimited' : plan.limitations.maxVideos}</span>
                    </div>
                    <div className="limit-item">
                      <span>Events: </span>
                      <span>{plan.limitations.maxEvents === -1 ? 'Unlimited' : plan.limitations.maxEvents}</span>
                    </div>
                  </div>
                </div>
                
                <div className="plan-action">
                  {isAuthenticated ? (
                    <Link to="/dashboard" className={`btn ${planKey === 'platinum' ? 'btn-primary' : 'btn-outline'}`}>
                      Go to Dashboard
                    </Link>
                  ) : (
                    <Link to="/register" className={`btn ${planKey === 'platinum' ? 'btn-primary' : 'btn-outline'}`}>
                      Choose {plan.plan.toUpperCase()}
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Comparison Table */}
        <div className="comparison-section">
          <h2>Plan Comparison</h2>
          <div className="comparison-table-container">
            <table className="comparison-table">
              <thead>
                <tr>
                  <th>Feature</th>
                  <th>Individual</th>
                  <th>Celebrity</th>
                  <th>Mandal Free</th>
                  <th>Mandal Gold</th>
                  <th>Mandal Platinum</th>
                  <th>Mandal Diamond</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Price</td>
                  <td>Free</td>
                  <td>Free</td>
                  <td>Free</td>
                  <td>₹2,999/year</td>
                  <td>₹4,999/year</td>
                  <td>₹9,999/year</td>
                </tr>
                <tr>
                  <td>Max Photos</td>
                  <td>5</td>
                  <td>20</td>
                  <td>10</td>
                  <td>50</td>
                  <td>100</td>
                  <td>Unlimited</td>
                </tr>
                <tr>
                  <td>Max Videos</td>
                  <td>0</td>
                  <td>5</td>
                  <td>0</td>
                  <td>10</td>
                  <td>25</td>
                  <td>Unlimited</td>
                </tr>
                <tr>
                  <td>Featured Listing</td>
                  <td>❌</td>
                  <td>✅</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>✅</td>
                  <td>✅</td>
                </tr>
                <tr>
                  <td>Analytics</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>✅</td>
                  <td>✅</td>
                  <td>✅</td>
                </tr>
                <tr>
                  <td>Priority Support</td>
                  <td>❌</td>
                  <td>✅</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>✅</td>
                  <td>✅</td>
                </tr>
                <tr>
                  <td>Custom Branding</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>❌</td>
                  <td>✅</td>
                  <td>✅</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="faq-section">
          <h2>Frequently Asked Questions</h2>
          <div className="faq-grid">
            <div className="faq-item">
              <h4>Can I upgrade my plan later?</h4>
              <p>Yes, you can upgrade your Mandal plan at any time. The new features will be available immediately.</p>
            </div>
            <div className="faq-item">
              <h4>How does Celebrity verification work?</h4>
              <p>After registering as a Celebrity, our team will review your profile and verify your identity. This usually takes 2-3 business days.</p>
            </div>
            <div className="faq-item">
              <h4>What payment methods do you accept?</h4>
              <p>We accept all major credit cards, debit cards, UPI, and net banking for Mandal subscriptions.</p>
            </div>
            <div className="faq-item">
              <h4>Is there a refund policy?</h4>
              <p>Yes, we offer a 30-day money-back guarantee for all paid Mandal plans if you're not satisfied.</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="pricing-cta">
          <h2>Ready to Get Started?</h2>
          <p>Join thousands of devotees showcasing their Ganesha celebrations</p>
          {!isAuthenticated && (
            <Link to="/register" className="btn btn-primary btn-large">
              Create Your Account
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Pricing;
