import React from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Paper,
  Avatar,
  Stack,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  PhotoCamera as PhotoCameraIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated, user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const features = [
    {
      icon: <HomeIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      title: 'Home Celebrations',
      description: 'Perfect for individual and family Ganesha celebrations',
      price: 'Free',
      features: [
        'Upload up to 5 photos',
        'Basic contact information',
        'Standard support',
        'Community access'
      ],
      color: 'primary'
    },
    {
      icon: <BusinessIcon sx={{ fontSize: 48, color: 'secondary.main' }} />,
      title: 'Mandal Organizations',
      description: 'Comprehensive solutions for community organizations',
      price: 'Starting at ₹5,001',
      features: [
        'Multiple pricing plans',
        'Enhanced listings',
        'Event management',
        'Analytics & insights',
        'Custom branding options'
      ],
      color: 'secondary',
      popular: true
    },
    {
      icon: <StarIcon sx={{ fontSize: 48, color: 'warning.main' }} />,
      title: 'Celebrity Features',
      description: 'Premium showcase for public figures and celebrities',
      price: 'Free',
      features: [
        'Verification badge',
        'Featured listings',
        'Priority support',
        'Social media integration',
        'Up to 20 photos & 5 videos'
      ],
      color: 'warning'
    }
  ];

  const stats = [
    { icon: <PeopleIcon />, number: '10K+', label: 'Active Users' },
    { icon: <BusinessIcon />, number: '500+', label: 'Mandals' },
    { icon: <PhotoCameraIcon />, number: '50K+', label: 'Photos Shared' }
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
          color: 'white',
          py: { xs: 6, md: 10 },
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Box textAlign="center" mb={6}>
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 700,
                mb: 2,
                textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              Welcome to{' '}
              <Box component="span" sx={{ color: '#fff3cd' }}>
                Live Ganesh
              </Box>
            </Typography>
            <Typography
              variant="h5"
              sx={{
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                mb: 4,
                opacity: 0.9,
                maxWidth: 600,
                mx: 'auto'
              }}
            >
              Showcase your Ganesha celebrations and connect with devotees worldwide
            </Typography>

            {/* Stats */}
            <Grid container spacing={4} justifyContent="center" mb={4}>
              {stats.map((stat, index) => (
                <Grid item xs={4} md={2} key={index}>
                  <Box textAlign="center">
                    <Avatar
                      sx={{
                        bgcolor: 'rgba(255, 255, 255, 0.2)',
                        width: 56,
                        height: 56,
                        mx: 'auto',
                        mb: 1
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {stat.number}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      {stat.label}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>

            {/* CTA Buttons */}
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={2}
              justifyContent="center"
              alignItems="center"
            >
              {!isAuthenticated ? (
                <>
                  <Button
                    component={Link}
                    to="/register"
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: 'white',
                      color: 'primary.main',
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      '&:hover': {
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    Get Started
                  </Button>
                  <Button
                    component={Link}
                    to="/pricing"
                    variant="outlined"
                    size="large"
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      '&:hover': {
                        borderColor: 'white',
                        bgcolor: 'rgba(255, 255, 255, 0.1)',
                      },
                    }}
                  >
                    View Pricing
                  </Button>
                </>
              ) : (
                <Box textAlign="center">
                  <Button
                    component={Link}
                    to="/dashboard"
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: 'white',
                      color: 'primary.main',
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      mb: 2,
                      '&:hover': {
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                      },
                    }}
                  >
                    Go to Dashboard
                  </Button>
                  <Typography variant="h6" sx={{ opacity: 0.9 }}>
                    Welcome back, {user?.name}! 🙏
                  </Typography>
                </Box>
              )}
            </Stack>
          </Box>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography
          variant="h2"
          textAlign="center"
          sx={{
            mb: 6,
            fontWeight: 600,
            color: 'text.primary'
          }}
        >
          Three Categories for Everyone
        </Typography>

        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  border: feature.popular ? 3 : 1,
                  borderColor: feature.popular ? 'primary.main' : 'divider',
                  transform: feature.popular ? 'scale(1.05)' : 'none',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: feature.popular ? 'scale(1.08)' : 'scale(1.03)',
                    boxShadow: 6,
                  },
                }}
              >
                {feature.popular && (
                  <Chip
                    label="Most Popular"
                    color="primary"
                    sx={{
                      position: 'absolute',
                      top: -12,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      fontWeight: 600,
                      zIndex: 1,
                    }}
                  />
                )}

                <CardContent sx={{ flexGrow: 1, textAlign: 'center', pt: feature.popular ? 4 : 3 }}>
                  <Box mb={2}>
                    {feature.icon}
                  </Box>

                  <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                    {feature.title}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {feature.description}
                  </Typography>

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="h4" color="primary.main" sx={{ fontWeight: 700 }}>
                      {feature.price}
                    </Typography>
                    {feature.price.includes('₹') && (
                      <Typography variant="body2" color="text.secondary">
                        per year
                      </Typography>
                    )}
                  </Box>

                  <Box sx={{ textAlign: 'left' }}>
                    {feature.features.map((item, idx) => (
                      <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <CheckCircleIcon sx={{ color: 'success.main', mr: 1, fontSize: 20 }} />
                        <Typography variant="body2">{item}</Typography>
                      </Box>
                    ))}
                  </Box>
                </CardContent>

                <CardActions sx={{ p: 3, pt: 0 }}>
                  <Button
                    component={Link}
                    to={isAuthenticated ? "/dashboard" : "/register"}
                    variant={feature.popular ? "contained" : "outlined"}
                    color={feature.color}
                    fullWidth
                    size="large"
                  >
                    {isAuthenticated ? "Go to Dashboard" : "Get Started"}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* How It Works */}
      <Box sx={{ bgcolor: 'background.default', py: 8 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            textAlign="center"
            sx={{ mb: 6, fontWeight: 600, color: 'text.primary' }}
          >
            How It Works
          </Typography>

          <Grid container spacing={4}>
            {[
              {
                step: '1',
                title: 'Choose Your Category',
                description: 'Select from Home, Mandal, or Celebrity account types',
                icon: <TrendingUpIcon />
              },
              {
                step: '2',
                title: 'Create Your Profile',
                description: 'Fill in your details and upload your Ganesha photos',
                icon: <PhotoCameraIcon />
              },
              {
                step: '3',
                title: 'Share & Connect',
                description: 'Share your celebrations with devotees worldwide',
                icon: <PeopleIcon />
              }
            ].map((item, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Paper
                  sx={{
                    p: 4,
                    textAlign: 'center',
                    height: '100%',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    },
                  }}
                >
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      bgcolor: 'primary.main',
                      mx: 'auto',
                      mb: 2,
                      fontSize: '2rem',
                      fontWeight: 700,
                    }}
                  >
                    {item.step}
                  </Avatar>
                  <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
                    {item.title}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {item.description}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h2" sx={{ mb: 2, fontWeight: 600 }}>
            Ready to Showcase Your Ganesha?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
            Join thousands of devotees sharing their celebrations
          </Typography>

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="center"
            alignItems="center"
          >
            {!isAuthenticated ? (
              <>
                <Button
                  component={Link}
                  to="/register"
                  variant="contained"
                  size="large"
                  sx={{
                    bgcolor: 'white',
                    color: 'primary.main',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    '&:hover': {
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                    },
                  }}
                >
                  Start Your Journey
                </Button>
                <Button
                  component={Link}
                  to="/pricing"
                  variant="outlined"
                  size="large"
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    '&:hover': {
                      borderColor: 'white',
                      bgcolor: 'rgba(255, 255, 255, 0.1)',
                    },
                  }}
                >
                  Compare Plans
                </Button>
              </>
            ) : (
              <Button
                component={Link}
                to="/dashboard"
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                  },
                }}
              >
                Manage Your Listing
              </Button>
            )}
          </Stack>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
