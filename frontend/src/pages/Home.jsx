import React from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Paper,
  Avatar,
  Stack,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  PhotoCamera as PhotoCameraIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated, user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const features = [
    {
      icon: <HomeIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      title: 'Home Celebrations',
      description: 'Perfect for individual and family Ganesha celebrations',
      price: 'Free',
      features: [
        'Upload up to 5 photos',
        'Basic contact information',
        'Standard support',
        'Community access'
      ],
      color: 'primary'
    },
    {
      icon: <BusinessIcon sx={{ fontSize: 48, color: 'secondary.main' }} />,
      title: 'Mandal Organizations',
      description: 'Comprehensive solutions for community organizations',
      price: 'Starting at ₹5,001',
      features: [
        'Multiple pricing plans',
        'Enhanced listings',
        'Event management',
        'Analytics & insights',
        'Custom branding options'
      ],
      color: 'secondary',
      popular: true
    },
    {
      icon: <StarIcon sx={{ fontSize: 48, color: 'warning.main' }} />,
      title: 'Celebrity Features',
      description: 'Premium showcase for public figures and celebrities',
      price: 'Free',
      features: [
        'Verification badge',
        'Featured listings',
        'Priority support',
        'Social media integration',
        'Up to 20 photos & 5 videos'
      ],
      color: 'warning'
    }
  ];

  const stats = [
    { icon: <PeopleIcon />, number: '10K+', label: 'Active Users' },
    { icon: <BusinessIcon />, number: '500+', label: 'Mandals' },
    { icon: <PhotoCameraIcon />, number: '50K+', label: 'Photos Shared' }
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ff8c42 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
          minHeight: { xs: '80vh', md: '90vh' },
          display: 'flex',
          alignItems: 'center',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
            `,
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={6} alignItems="center">
            {/* Left Content */}
            <Grid item xs={12} md={6}>
              <Box>
                <Typography
                  variant="h1"
                  sx={{
                    fontSize: { xs: '2.8rem', md: '4rem', lg: '4.5rem' },
                    fontWeight: 800,
                    mb: 3,
                    lineHeight: 1.1,
                    textShadow: '2px 2px 8px rgba(0,0,0,0.3)',
                    background: 'linear-gradient(45deg, #fff, #fff3cd)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  Celebrate
                  <br />
                  <Box component="span" sx={{ color: '#fff3cd' }}>
                    Ganesh Utsav
                  </Box>
                  <br />
                  Digitally
                </Typography>

                <Typography
                  variant="h5"
                  sx={{
                    fontSize: { xs: '1.2rem', md: '1.4rem' },
                    mb: 4,
                    opacity: 0.95,
                    fontWeight: 400,
                    lineHeight: 1.6,
                  }}
                >
                  Connect with millions of devotees worldwide. Share your Ganesha celebrations,
                  discover beautiful mandals, and be part of the largest digital Ganesh community.
                </Typography>

                {/* Feature Highlights */}
                <Box sx={{ mb: 4 }}>
                  {[
                    '🏠 Individual & Family Celebrations',
                    '🏛️ Mandal Organizations & Events',
                    '⭐ Celebrity & Public Figure Showcases'
                  ].map((feature, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: '#fff3cd',
                          mr: 2,
                          boxShadow: '0 0 10px rgba(255, 243, 205, 0.5)',
                        }}
                      />
                      <Typography variant="body1" sx={{ fontSize: '1.1rem', opacity: 0.9 }}>
                        {feature}
                      </Typography>
                    </Box>
                  ))}
                </Box>

                {/* CTA Buttons */}
                <Stack
                  direction={{ xs: 'column', sm: 'row' }}
                  spacing={3}
                  sx={{ mb: 4 }}
                >
                  {!isAuthenticated ? (
                    <>
                      <Button
                        component={Link}
                        to="/register"
                        variant="contained"
                        size="large"
                        sx={{
                          bgcolor: 'white',
                          color: 'primary.main',
                          px: 5,
                          py: 2,
                          fontSize: '1.2rem',
                          fontWeight: 600,
                          borderRadius: 3,
                          boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.95)',
                            transform: 'translateY(-3px)',
                            boxShadow: '0 12px 40px rgba(0,0,0,0.3)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        Start Your Journey
                      </Button>
                      <Button
                        component={Link}
                        to="/pricing"
                        variant="outlined"
                        size="large"
                        sx={{
                          borderColor: 'white',
                          color: 'white',
                          px: 5,
                          py: 2,
                          fontSize: '1.2rem',
                          fontWeight: 600,
                          borderRadius: 3,
                          borderWidth: 2,
                          '&:hover': {
                            borderColor: 'white',
                            bgcolor: 'rgba(255, 255, 255, 0.15)',
                            transform: 'translateY(-2px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        View Plans
                      </Button>
                    </>
                  ) : (
                    <Box>
                      <Button
                        component={Link}
                        to="/dashboard"
                        variant="contained"
                        size="large"
                        sx={{
                          bgcolor: 'white',
                          color: 'primary.main',
                          px: 5,
                          py: 2,
                          fontSize: '1.2rem',
                          fontWeight: 600,
                          borderRadius: 3,
                          mb: 2,
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.95)',
                            transform: 'translateY(-2px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        Go to Dashboard
                      </Button>
                      <Typography variant="h6" sx={{ opacity: 0.9, mt: 2 }}>
                        Welcome back, {user?.name}! 🙏
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </Box>
            </Grid>

            {/* Right Content - Stats */}
            <Grid item xs={12} md={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Grid container spacing={3}>
                  {stats.map((stat, index) => (
                    <Grid item xs={4} key={index}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 3,
                          textAlign: 'center',
                          background: 'rgba(255, 255, 255, 0.15)',
                          backdropFilter: 'blur(10px)',
                          borderRadius: 3,
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          color: 'white',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-5px)',
                            background: 'rgba(255, 255, 255, 0.2)',
                          },
                        }}
                      >
                        <Box sx={{ mb: 2, color: '#fff3cd' }}>
                          {stat.icon}
                        </Box>
                        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                          {stat.number}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '0.9rem' }}>
                          {stat.label}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>

                {/* Floating Elements */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: '20%',
                    right: '10%',
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: 'rgba(255, 255, 255, 0.1)',
                    animation: 'float 6s ease-in-out infinite',
                    '@keyframes float': {
                      '0%, 100%': { transform: 'translateY(0px)' },
                      '50%': { transform: 'translateY(-20px)' },
                    },
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: '30%',
                    right: '5%',
                    width: 60,
                    height: 60,
                    borderRadius: '50%',
                    background: 'rgba(255, 255, 255, 0.08)',
                    animation: 'float 8s ease-in-out infinite reverse',
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: { xs: 8, md: 12 }, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 700,
                mb: 3,
                background: 'linear-gradient(45deg, #ff6b35, #f7931e)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Choose Your Perfect Plan
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 600, mx: 'auto', fontSize: '1.2rem' }}
            >
              Whether you're celebrating at home, managing a mandal, or showcasing as a celebrity -
              we have the perfect solution for your Ganesh celebrations
            </Typography>
          </Box>

          <Grid container spacing={4} justifyContent="center">
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card
                  elevation={feature.popular ? 20 : 8}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    borderRadius: 4,
                    overflow: 'visible',
                    background: feature.popular
                      ? 'linear-gradient(135deg, #ff6b35, #f7931e)'
                      : 'white',
                    color: feature.popular ? 'white' : 'inherit',
                    transform: feature.popular ? 'scale(1.05)' : 'none',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: feature.popular ? 'scale(1.08)' : 'scale(1.05)',
                      boxShadow: feature.popular ? 24 : 16,
                    },
                  }}
                >
                  {feature.popular && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: -15,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        zIndex: 2,
                      }}
                    >
                      <Chip
                        label="🔥 Most Popular"
                        sx={{
                          bgcolor: '#fff3cd',
                          color: '#ff6b35',
                          fontWeight: 700,
                          fontSize: '0.9rem',
                          px: 2,
                          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                        }}
                      />
                    </Box>
                  )}

                  <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 4, pt: feature.popular ? 5 : 4 }}>
                    {/* Icon */}
                    <Box
                      sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 80,
                        height: 80,
                        borderRadius: '50%',
                        bgcolor: feature.popular ? 'rgba(255, 255, 255, 0.2)' : `${feature.color}.light`,
                        color: feature.popular ? 'white' : `${feature.color}.main`,
                        mb: 3,
                        boxShadow: feature.popular ? 'none' : '0 8px 32px rgba(0,0,0,0.1)',
                      }}
                    >
                      {feature.icon}
                    </Box>

                    <Typography variant="h4" sx={{ mb: 2, fontWeight: 700 }}>
                      {feature.title}
                    </Typography>

                    <Typography
                      variant="body1"
                      sx={{
                        mb: 4,
                        opacity: feature.popular ? 0.9 : 0.7,
                        fontSize: '1.1rem',
                        lineHeight: 1.6,
                      }}
                    >
                      {feature.description}
                    </Typography>

                    {/* Price */}
                    <Box sx={{ mb: 4 }}>
                      <Typography
                        variant="h3"
                        sx={{
                          fontWeight: 800,
                          color: feature.popular ? '#fff3cd' : 'primary.main',
                          mb: 1,
                        }}
                      >
                        {feature.price}
                      </Typography>
                      {feature.price.includes('₹') && (
                        <Typography
                          variant="body2"
                          sx={{ opacity: feature.popular ? 0.8 : 0.6 }}
                        >
                          per year
                        </Typography>
                      )}
                    </Box>

                    {/* Features List */}
                    <Box sx={{ textAlign: 'left', mb: 3 }}>
                      {feature.features.map((item, idx) => (
                        <Box key={idx} sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                          <CheckCircleIcon
                            sx={{
                              color: feature.popular ? '#fff3cd' : 'success.main',
                              mr: 2,
                              fontSize: 20,
                              mt: 0.2,
                            }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: '1rem',
                              opacity: feature.popular ? 0.9 : 0.8,
                            }}
                          >
                            {item}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>

                  <CardActions sx={{ p: 4, pt: 0 }}>
                    <Button
                      component={Link}
                      to={isAuthenticated ? "/dashboard" : "/register"}
                      variant={feature.popular ? "outlined" : "contained"}
                      fullWidth
                      size="large"
                      sx={{
                        py: 2,
                        fontSize: '1.1rem',
                        fontWeight: 600,
                        borderRadius: 3,
                        ...(feature.popular ? {
                          borderColor: 'white',
                          color: 'white',
                          borderWidth: 2,
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.1)',
                            borderColor: 'white',
                          },
                        } : {
                          background: `linear-gradient(45deg, ${theme.palette[feature.color].main}, ${theme.palette[feature.color].dark})`,
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 8px 25px rgba(0,0,0,0.2)',
                          },
                        }),
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {isAuthenticated ? "Go to Dashboard" : "Get Started"}
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Trust Indicators */}
          <Box sx={{ mt: 8, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
              Trusted by thousands of devotees across India
            </Typography>
            <Stack
              direction="row"
              spacing={4}
              justifyContent="center"
              alignItems="center"
              sx={{ opacity: 0.6 }}
            >
              <Typography variant="body2">🏆 Award Winning Platform</Typography>
              <Typography variant="body2">🔒 100% Secure</Typography>
              <Typography variant="body2">📱 Mobile Friendly</Typography>
              <Typography variant="body2">⚡ Lightning Fast</Typography>
            </Stack>
          </Box>
        </Container>
      </Box>

      {/* How It Works */}
      <Box sx={{ py: { xs: 8, md: 12 }, bgcolor: 'white' }}>
        <Container maxWidth="lg">
          <Box textAlign="center" mb={8}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 700,
                mb: 3,
                color: 'text.primary'
              }}
            >
              How It Works
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ maxWidth: 500, mx: 'auto', fontSize: '1.2rem' }}
            >
              Get started in just 3 simple steps and join the celebration
            </Typography>
          </Box>

          <Grid container spacing={6} alignItems="center">
            {[
              {
                step: '01',
                title: 'Choose Your Category',
                description: 'Select from Individual, Mandal, or Celebrity account types based on your celebration style',
                icon: <TrendingUpIcon sx={{ fontSize: 40 }} />,
                color: 'primary'
              },
              {
                step: '02',
                title: 'Create Your Profile',
                description: 'Fill in your details, upload beautiful Ganesha photos, and customize your showcase',
                icon: <PhotoCameraIcon sx={{ fontSize: 40 }} />,
                color: 'secondary'
              },
              {
                step: '03',
                title: 'Share & Connect',
                description: 'Share your celebrations with millions of devotees and discover amazing mandals worldwide',
                icon: <PeopleIcon sx={{ fontSize: 40 }} />,
                color: 'success'
              }
            ].map((item, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Box
                  sx={{
                    position: 'relative',
                    textAlign: 'center',
                    p: 4,
                  }}
                >
                  {/* Step Number Background */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -20,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: 120,
                      height: 120,
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, ${theme.palette[item.color].light}, ${theme.palette[item.color].main})`,
                      opacity: 0.1,
                      zIndex: 0,
                    }}
                  />

                  {/* Step Number */}
                  <Typography
                    variant="h2"
                    sx={{
                      fontSize: '4rem',
                      fontWeight: 800,
                      color: `${item.color}.main`,
                      opacity: 0.3,
                      mb: 2,
                      position: 'relative',
                      zIndex: 1,
                    }}
                  >
                    {item.step}
                  </Typography>

                  {/* Icon */}
                  <Box
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      bgcolor: `${item.color}.main`,
                      color: 'white',
                      mb: 3,
                      boxShadow: `0 8px 32px ${theme.palette[item.color].main}40`,
                      position: 'relative',
                      zIndex: 2,
                    }}
                  >
                    {item.icon}
                  </Box>

                  <Typography variant="h5" sx={{ mb: 2, fontWeight: 700 }}>
                    {item.title}
                  </Typography>

                  <Typography
                    variant="body1"
                    color="text.secondary"
                    sx={{
                      fontSize: '1.1rem',
                      lineHeight: 1.6,
                      maxWidth: 280,
                      mx: 'auto',
                    }}
                  >
                    {item.description}
                  </Typography>

                  {/* Connector Line */}
                  {index < 2 && !isMobile && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: '50%',
                        right: -30,
                        width: 60,
                        height: 2,
                        background: `linear-gradient(90deg, ${theme.palette[item.color].main}, ${theme.palette.primary.light})`,
                        opacity: 0.3,
                        zIndex: 0,
                      }}
                    />
                  )}
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Additional Info */}
          <Box sx={{ mt: 8, textAlign: 'center' }}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                bgcolor: 'primary.light',
                color: 'primary.contrastText',
                borderRadius: 4,
                background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
              }}
            >
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                🎉 Ready to get started?
              </Typography>
              <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
                Join thousands of devotees already celebrating digitally
              </Typography>
              <Button
                component={Link}
                to="/register"
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  px: 4,
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                Create Your Account
              </Button>
            </Paper>
          </Box>
        </Container>
      </Box>

      {/* Final CTA Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ff8c42 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)
            `,
          }
        }}
      >
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={6} alignItems="center">
            {/* Left Content */}
            <Grid item xs={12} md={8}>
              <Typography
                variant="h2"
                sx={{
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                  fontWeight: 800,
                  mb: 3,
                  lineHeight: 1.2,
                }}
              >
                Ready to Showcase Your Ganesha?
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  mb: 4,
                  opacity: 0.9,
                  fontSize: '1.3rem',
                  lineHeight: 1.6,
                }}
              >
                Join thousands of devotees sharing their celebrations and be part of the largest
                digital Ganesh community in India. Start your spiritual journey today!
              </Typography>

              {/* Features List */}
              <Grid container spacing={2} sx={{ mb: 4 }}>
                {[
                  '✨ Free for Individual & Celebrity accounts',
                  '🚀 Instant setup in under 5 minutes',
                  '📱 Mobile-friendly platform',
                  '🔒 100% secure and private'
                ].map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body1" sx={{ fontSize: '1.1rem', opacity: 0.9 }}>
                        {feature}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>

              <Stack
                direction={{ xs: 'column', sm: 'row' }}
                spacing={3}
                sx={{ mb: { xs: 4, md: 0 } }}
              >
                {!isAuthenticated ? (
                  <>
                    <Button
                      component={Link}
                      to="/register"
                      variant="contained"
                      size="large"
                      sx={{
                        bgcolor: 'white',
                        color: 'primary.main',
                        px: 5,
                        py: 2,
                        fontSize: '1.2rem',
                        fontWeight: 600,
                        borderRadius: 3,
                        boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
                        '&:hover': {
                          bgcolor: 'rgba(255, 255, 255, 0.95)',
                          transform: 'translateY(-3px)',
                          boxShadow: '0 12px 40px rgba(0,0,0,0.3)',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      Start Your Journey Free
                    </Button>
                    <Button
                      component={Link}
                      to="/pricing"
                      variant="outlined"
                      size="large"
                      sx={{
                        borderColor: 'white',
                        color: 'white',
                        px: 5,
                        py: 2,
                        fontSize: '1.2rem',
                        fontWeight: 600,
                        borderRadius: 3,
                        borderWidth: 2,
                        '&:hover': {
                          borderColor: 'white',
                          bgcolor: 'rgba(255, 255, 255, 0.15)',
                          transform: 'translateY(-2px)',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      View All Plans
                    </Button>
                  </>
                ) : (
                  <Button
                    component={Link}
                    to="/dashboard"
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: 'white',
                      color: 'primary.main',
                      px: 5,
                      py: 2,
                      fontSize: '1.2rem',
                      fontWeight: 600,
                      borderRadius: 3,
                      '&:hover': {
                        bgcolor: 'rgba(255, 255, 255, 0.95)',
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                  >
                    Manage Your Celebration
                  </Button>
                )}
              </Stack>
            </Grid>

            {/* Right Content - Testimonial/Stats */}
            <Grid item xs={12} md={4}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  background: 'rgba(255, 255, 255, 0.15)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 4,
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  textAlign: 'center',
                }}
              >
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
                  10,000+
                </Typography>
                <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
                  Happy Devotees
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="body1" sx={{ fontStyle: 'italic', opacity: 0.9, mb: 2 }}>
                    "Live Ganesh made our celebration reach thousands of devotees.
                    The platform is amazing!"
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    - Mumbai Sarvajanik Mandal
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <StarIcon key={star} sx={{ color: '#fff3cd', fontSize: 20 }} />
                  ))}
                </Box>
                <Typography variant="body2" sx={{ mt: 1, opacity: 0.8 }}>
                  4.9/5 Rating
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
