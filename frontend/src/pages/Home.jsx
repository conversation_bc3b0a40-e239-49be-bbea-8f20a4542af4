import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import './Home.css';

const Home = () => {
  const { isAuthenticated, user } = useAuth();

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1 className="hero-title">
            Welcome to <span className="highlight">Live Ganesh</span>
          </h1>
          <p className="hero-subtitle">
            Showcase your Ganesha celebrations and connect with devotees worldwide
          </p>
          <div className="hero-stats">
            <div className="stat">
              <span className="stat-number">🏠</span>
              <span className="stat-label">Home Celebrations</span>
            </div>
            <div className="stat">
              <span className="stat-number">🏛️</span>
              <span className="stat-label">Mandal Listings</span>
            </div>
            <div className="stat">
              <span className="stat-number">⭐</span>
              <span className="stat-label">Celebrity Features</span>
            </div>
          </div>
          
          {!isAuthenticated ? (
            <div className="hero-actions">
              <Link to="/register" className="btn btn-primary">
                Get Started
              </Link>
              <Link to="/pricing" className="btn btn-secondary">
                View Pricing
              </Link>
            </div>
          ) : (
            <div className="hero-actions">
              <Link to="/dashboard" className="btn btn-primary">
                Go to Dashboard
              </Link>
              <p className="welcome-message">
                Welcome back, {user?.name}! 🙏
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <h2 className="section-title">Three Categories for Everyone</h2>
          
          <div className="features-grid">
            {/* Individual/Home */}
            <div className="feature-card">
              <div className="feature-icon">🏠</div>
              <h3>Home Celebrations</h3>
              <p>Perfect for individual and family Ganesha celebrations</p>
              <ul className="feature-list">
                <li>✅ Free forever</li>
                <li>✅ Upload up to 5 photos</li>
                <li>✅ Basic contact information</li>
                <li>✅ Standard support</li>
              </ul>
              <div className="feature-price">
                <span className="price">Free</span>
              </div>
            </div>

            {/* Mandal */}
            <div className="feature-card featured">
              <div className="feature-badge">Most Popular</div>
              <div className="feature-icon">🏛️</div>
              <h3>Mandal Organizations</h3>
              <p>Comprehensive solutions for community organizations</p>
              <ul className="feature-list">
                <li>✅ Multiple pricing plans</li>
                <li>✅ Enhanced listings</li>
                <li>✅ Event management</li>
                <li>✅ Analytics & insights</li>
                <li>✅ Custom branding options</li>
              </ul>
              <div className="feature-price">
                <span className="price">Starting at ₹2,999</span>
                <span className="price-period">/year</span>
              </div>
            </div>

            {/* Celebrity */}
            <div className="feature-card">
              <div className="feature-icon">⭐</div>
              <h3>Celebrity Features</h3>
              <p>Premium showcase for public figures and celebrities</p>
              <ul className="feature-list">
                <li>✅ Free with verification</li>
                <li>✅ Verification badge</li>
                <li>✅ Featured listings</li>
                <li>✅ Priority support</li>
                <li>✅ Social media integration</li>
              </ul>
              <div className="feature-price">
                <span className="price">Free</span>
                <span className="price-note">*Verification required</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="how-it-works">
        <div className="container">
          <h2 className="section-title">How It Works</h2>
          
          <div className="steps">
            <div className="step">
              <div className="step-number">1</div>
              <h3>Choose Your Category</h3>
              <p>Select from Home, Mandal, or Celebrity account types</p>
            </div>
            
            <div className="step">
              <div className="step-number">2</div>
              <h3>Create Your Profile</h3>
              <p>Fill in your details and upload your Ganesha photos</p>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <h3>Share & Connect</h3>
              <p>Share your celebrations with devotees worldwide</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Showcase Your Ganesha?</h2>
            <p>Join thousands of devotees sharing their celebrations</p>
            
            {!isAuthenticated ? (
              <div className="cta-actions">
                <Link to="/register" className="btn btn-primary btn-large">
                  Start Your Journey
                </Link>
                <Link to="/pricing" className="btn btn-outline">
                  Compare Plans
                </Link>
              </div>
            ) : (
              <div className="cta-actions">
                <Link to="/dashboard" className="btn btn-primary btn-large">
                  Manage Your Listing
                </Link>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
