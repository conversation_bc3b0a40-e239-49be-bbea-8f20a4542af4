import React, { useState, useEffect } from 'react';
import { useP<PERSON><PERSON>, Link } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardMedia,
  Button,
  Breadcrumbs,
  Paper,
  IconButton,
  Dialog,
  DialogContent,
  Chip,
  Rating,
  Divider,
  Avatar,
  Stack,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Favorite as FavoriteIcon,
  Share as ShareIcon,
  LocationOn as LocationOnIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Close as CloseIcon,
  Volunteer as VolunteerIcon
} from '@mui/icons-material';

const ListingDetail = () => {
  const { id } = useParams();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [selectedImage, setSelectedImage] = useState(null);
  const [videoDialogOpen, setVideoDialogOpen] = useState(false);

  // Mock data - replace with API call
  const listing = {
    id: 1,
    title: "Shree Siddhivinayak Temple",
    location: "Mumbai, Maharashtra",
    category: "Mandal",
    type: "Premium",
    rating: 4.9,
    views: "50K+",
    description: "The Shree Siddhivinayak Temple is a Hindu temple dedicated to Lord Ganesha. It is located in Mumbai, Maharashtra, India. The temple was originally built in 1801 and has undergone several renovations. It is one of the most visited temples in Mumbai and attracts devotees from all over the world.",
    mainImage: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800",
    photos: [
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400",
      "https://images.unsplash.com/photo-1582510003544-4d00b7f74220?w=400",
      "https://images.unsplash.com/photo-1580477667995-2b94f01c9516?w=400",
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400"
    ],
    videoThumbnail: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600",
    contact: {
      phone: "+91-22-24224438",
      email: "<EMAIL>"
    }
  };

  const handleImageClick = (image) => {
    setSelectedImage(image);
  };

  const handleVideoPlay = () => {
    setVideoDialogOpen(true);
  };

  return (
    <Box sx={{ bgcolor: 'white', minHeight: '100vh' }}>
      <Container maxWidth="lg" sx={{ py: 6 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs
          sx={{ mb: 4, color: 'text.secondary' }}
          separator="/"
        >
          <Link
            to="/"
            style={{
              color: theme.palette.text.secondary,
              textDecoration: 'none',
              fontSize: '0.875rem'
            }}
          >
            Home
          </Link>
          <Typography color="text.primary" fontSize="0.875rem">
            {listing.title}
          </Typography>
        </Breadcrumbs>

        {/* Header Section */}
        <Box sx={{ mb: 6 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
            <Box>
              <Typography
                variant="h2"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  fontSize: { xs: '2rem', md: '3rem' },
                  color: 'text.primary',
                  lineHeight: 1.2
                }}
              >
                {listing.title}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <LocationOnIcon sx={{ color: 'text.secondary', mr: 1, fontSize: 20 }} />
                  <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 500 }}>
                    {listing.location}
                  </Typography>
                </Box>
                <Chip
                  label={listing.category}
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    fontWeight: 600,
                    textTransform: 'capitalize'
                  }}
                />
                <Chip
                  label={listing.type}
                  sx={{
                    bgcolor: 'secondary.main',
                    color: 'white',
                    fontWeight: 600
                  }}
                />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Rating value={listing.rating} precision={0.1} readOnly />
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {listing.rating}
                  </Typography>
                </Box>
                <Typography variant="body1" color="text.secondary">
                  {listing.views} views
                </Typography>
              </Box>
            </Box>

            {!isMobile && (
              <Box sx={{ display: 'flex', gap: 2 }}>
                <IconButton
                  sx={{
                    bgcolor: 'grey.100',
                    color: 'text.primary',
                    '&:hover': { bgcolor: 'grey.200' },
                    width: 48,
                    height: 48
                  }}
                >
                  <FavoriteIcon />
                </IconButton>
                <IconButton
                  sx={{
                    bgcolor: 'grey.100',
                    color: 'text.primary',
                    '&:hover': { bgcolor: 'grey.200' },
                    width: 48,
                    height: 48
                  }}
                >
                  <ShareIcon />
                </IconButton>
              </Box>
            )}
          </Box>
        </Box>

        {/* Main Image */}
        <Box sx={{ mb: 8 }}>
          <Card
            elevation={0}
            sx={{
              borderRadius: 4,
              overflow: 'hidden',
              border: '1px solid',
              borderColor: 'grey.200'
            }}
          >
            <CardMedia
              component="img"
              height={isMobile ? "300" : "500"}
              image={listing.mainImage}
              alt={listing.title}
              sx={{ objectFit: 'cover' }}
            />
          </Card>
        </Box>

        {/* About Section */}
        <Box sx={{ mb: 4 }}>
          <Typography 
            variant="h4" 
            sx={{ 
              fontWeight: 700, 
              mb: 2,
              color: 'text.primary'
            }}
          >
            About
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              lineHeight: 1.7,
              color: 'text.primary',
              fontSize: '1.1rem'
            }}
          >
            {listing.description}
          </Typography>
        </Box>

        {/* Photos Section */}
        <Box sx={{ mb: 4 }}>
          <Typography 
            variant="h4" 
            sx={{ 
              fontWeight: 700, 
              mb: 3,
              color: 'text.primary'
            }}
          >
            Photos
          </Typography>
          <Grid container spacing={2}>
            {listing.photos.map((photo, index) => (
              <Grid item xs={6} sm={4} md={3} key={index}>
                <Card 
                  elevation={2}
                  sx={{ 
                    borderRadius: 2, 
                    overflow: 'hidden',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.05)',
                      boxShadow: '0 8px 25px rgba(164, 36, 59, 0.15)'
                    }
                  }}
                  onClick={() => handleImageClick(photo)}
                >
                  <CardMedia
                    component="img"
                    height="150"
                    image={photo}
                    alt={`Photo ${index + 1}`}
                    sx={{ objectFit: 'cover' }}
                  />
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Videos Section */}
        <Box sx={{ mb: 4 }}>
          <Typography 
            variant="h4" 
            sx={{ 
              fontWeight: 700, 
              mb: 3,
              color: 'text.primary'
            }}
          >
            Videos
          </Typography>
          <Card 
            elevation={3}
            sx={{ 
              borderRadius: 3, 
              overflow: 'hidden',
              position: 'relative',
              cursor: 'pointer'
            }}
            onClick={handleVideoPlay}
          >
            <CardMedia
              component="img"
              height={isMobile ? "200" : "300"}
              image={listing.videoThumbnail}
              alt="Video thumbnail"
              sx={{ objectFit: 'cover' }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'rgba(0, 0, 0, 0.4)',
              }}
            >
              <IconButton
                sx={{
                  bgcolor: 'rgba(0, 0, 0, 0.6)',
                  color: 'white',
                  width: 80,
                  height: 80,
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.8)',
                  },
                }}
              >
                <PlayArrowIcon sx={{ fontSize: 40 }} />
              </IconButton>
            </Box>
          </Card>
        </Box>

        {/* Donation Section */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 2,
              color: 'text.primary'
            }}
          >
            Donation
          </Typography>
          <Typography
            variant="body1"
            sx={{
              lineHeight: 1.7,
              color: 'text.primary',
              fontSize: '1.1rem',
              mb: 3
            }}
          >
            Your generous donations help us maintain the temple and continue our services to the community. We appreciate your support.
          </Typography>
          <Button
            variant="contained"
            size="large"
            startIcon={<VolunteerIcon />}
            sx={{
              background: 'linear-gradient(135deg, #a4243b, #d8973c)',
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600,
              '&:hover': {
                background: 'linear-gradient(135deg, #7a1b2c, #b8762a)',
                transform: 'translateY(-2px)',
              },
            }}
          >
            Donate Now
          </Button>
        </Box>

        {/* Contact Section */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 3,
              color: 'text.primary'
            }}
          >
            Contact
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, rgba(164, 36, 59, 0.05), rgba(216, 151, 60, 0.05))',
                  border: '1px solid rgba(164, 36, 59, 0.1)',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PhoneIcon sx={{ color: 'primary.main', mr: 2 }} />
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Phone
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {listing.contact.phone}
                    </Typography>
                  </Box>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <EmailIcon sx={{ color: 'primary.main', mr: 2 }} />
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Email
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {listing.contact.email}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Box>

        {/* Location Section */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              mb: 3,
              color: 'text.primary'
            }}
          >
            Location
          </Typography>
          <Card elevation={3} sx={{ borderRadius: 3, overflow: 'hidden' }}>
            <CardMedia
              component="img"
              height={isMobile ? "200" : "300"}
              image="https://images.unsplash.com/photo-1577717903315-1691ae25ab3f?w=800"
              alt="Location map"
              sx={{ objectFit: 'cover' }}
            />
          </Card>
        </Box>

        {/* Mobile Action Buttons */}
        {isMobile && (
          <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              bgcolor: 'white',
              p: 2,
              boxShadow: '0 -4px 20px rgba(0,0,0,0.1)',
              zIndex: 1000
            }}
          >
            <Stack direction="row" spacing={2}>
              <IconButton
                sx={{
                  bgcolor: 'neutral.main',
                  color: 'neutral.contrastText',
                  '&:hover': { bgcolor: 'neutral.dark' }
                }}
              >
                <FavoriteIcon />
              </IconButton>
              <IconButton
                sx={{
                  bgcolor: 'neutral.main',
                  color: 'neutral.contrastText',
                  '&:hover': { bgcolor: 'neutral.dark' }
                }}
              >
                <ShareIcon />
              </IconButton>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  background: 'linear-gradient(135deg, #a4243b, #d8973c)',
                  fontWeight: 600,
                  '&:hover': {
                    background: 'linear-gradient(135deg, #7a1b2c, #b8762a)',
                  },
                }}
              >
                Contact Now
              </Button>
            </Stack>
          </Box>
        )}
      </Container>

      {/* Image Dialog */}
      <Dialog
        open={Boolean(selectedImage)}
        onClose={() => setSelectedImage(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogContent sx={{ p: 0, position: 'relative' }}>
          <IconButton
            onClick={() => setSelectedImage(null)}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              zIndex: 1,
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
          {selectedImage && (
            <img
              src={selectedImage}
              alt="Full size"
              style={{
                width: '100%',
                height: 'auto',
                display: 'block',
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Video Dialog */}
      <Dialog
        open={videoDialogOpen}
        onClose={() => setVideoDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogContent sx={{ p: 0, position: 'relative' }}>
          <IconButton
            onClick={() => setVideoDialogOpen(false)}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'rgba(0, 0, 0, 0.5)',
              color: 'white',
              zIndex: 1,
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.7)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
          <Box sx={{ aspectRatio: '16/9', bgcolor: 'black', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Typography color="white">Video Player Placeholder</Typography>
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ListingDetail;
