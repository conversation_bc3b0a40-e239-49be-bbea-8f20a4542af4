import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  Tab,
  Tabs,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  useMediaQuery,
  CardMedia,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  PhotoCamera as PhotoCameraIcon,
  VideoLibrary as VideoLibraryIcon,
  Link as LinkIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  Star as StarIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  Upload as UploadIcon,
  YouTube as YouTubeIcon,
  Facebook as FacebookIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [selectedTab, setSelectedTab] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [formData, setFormData] = useState({});
  
  // Mock data - replace with API calls
  const [userStats, setUserStats] = useState({
    totalListings: 3,
    totalViews: 1250,
    totalLikes: 89,
    totalPhotos: 15,
    totalVideos: 4
  });

  const [userListings, setUserListings] = useState([
    {
      id: 1,
      title: "My Home Ganesh 2024",
      category: "Home",
      status: "Active",
      views: 450,
      likes: 23,
      photos: 8,
      videos: 2,
      createdAt: "2024-08-15",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300"
    },
    {
      id: 2,
      title: "Community Mandal Celebration",
      category: "Mandal",
      status: "Active",
      views: 680,
      likes: 45,
      photos: 12,
      videos: 3,
      createdAt: "2024-08-10",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300"
    },
    {
      id: 3,
      title: "Eco-Friendly Ganesh",
      category: "Home",
      status: "Draft",
      views: 120,
      likes: 21,
      photos: 5,
      videos: 1,
      createdAt: "2024-08-20",
      image: "https://images.unsplash.com/photo-1582510003544-4d00b7f74220?w=300"
    }
  ]);

  const [mediaLibrary, setMediaLibrary] = useState([
    {
      id: 1,
      type: 'image',
      url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',
      title: 'Ganesh Photo 1',
      uploadDate: '2024-08-15'
    },
    {
      id: 2,
      type: 'video',
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300',
      title: 'Celebration Video',
      uploadDate: '2024-08-14'
    },
    {
      id: 3,
      type: 'youtube',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      title: 'Ganesh Aarti',
      uploadDate: '2024-08-13'
    }
  ]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleOpenDialog = (type, data = {}) => {
    setDialogType(type);
    setFormData(data);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDialogType('');
    setFormData({});
  };

  const handleFormSubmit = () => {
    // Handle form submission based on dialogType
    console.log('Form submitted:', dialogType, formData);
    handleCloseDialog();
  };

  const getUserTypeColor = (userType) => {
    switch (userType?.toLowerCase()) {
      case 'individual': return 'primary';
      case 'mandal': return 'secondary';
      case 'celebrity': return 'warning';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'success';
      case 'draft': return 'warning';
      case 'inactive': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ bgcolor: 'grey.50', minHeight: '100vh', py: 4 }}>
      <Container maxWidth="xl">
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                  fontWeight: 700
                }}
              >
                {user?.name?.charAt(0).toUpperCase()}
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                Welcome back, {user?.name}!
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Typography variant="body1" color="text.secondary">
                  {user?.email}
                </Typography>
                <Chip
                  label={user?.userType?.toUpperCase()}
                  color={getUserTypeColor(user?.userType)}
                  size="small"
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Manage your Ganesh celebrations and connect with devotees
              </Typography>
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog('listing')}
                sx={{ mr: 2 }}
              >
                Add New Listing
              </Button>
              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                onClick={() => handleOpenDialog('profile')}
              >
                Settings
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
                  {userStats.totalListings}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Listings
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main', mb: 1 }}>
                  {userStats.totalViews.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Views
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'error.main', mb: 1 }}>
                  {userStats.totalLikes}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Likes
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main', mb: 1 }}>
                  {userStats.totalPhotos}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Photos
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main', mb: 1 }}>
                  {userStats.totalVideos}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Videos
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200', mb: 4 }}>
          <Tabs
            value={selectedTab}
            onChange={handleTabChange}
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1rem',
              },
            }}
          >
            <Tab label="My Listings" />
            <Tab label="Media Library" />
            <Tab label="Page Management" />
            <Tab label="Analytics" />
          </Tabs>

          {/* Tab Content */}
          <Box sx={{ p: 3 }}>
            {selectedTab === 0 && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Your Ganesh Listings
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog('listing')}
                  >
                    Create New Listing
                  </Button>
                </Box>

                <Grid container spacing={3}>
                  {userListings.map((listing) => (
                    <Grid item xs={12} md={6} lg={4} key={listing.id}>
                      <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
                        <CardMedia
                          component="img"
                          height="200"
                          image={listing.image}
                          alt={listing.title}
                        />
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600, flex: 1 }}>
                              {listing.title}
                            </Typography>
                            <Chip
                              label={listing.status}
                              color={getStatusColor(listing.status)}
                              size="small"
                            />
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            {listing.category === 'Home' ? <HomeIcon fontSize="small" /> : <BusinessIcon fontSize="small" />}
                            <Typography variant="body2" color="text.secondary">
                              {listing.category}
                            </Typography>
                          </Box>

                          <Grid container spacing={2} sx={{ mb: 2 }}>
                            <Grid item xs={4}>
                              <Typography variant="body2" color="text.secondary">Views</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>{listing.views}</Typography>
                            </Grid>
                            <Grid item xs={4}>
                              <Typography variant="body2" color="text.secondary">Likes</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>{listing.likes}</Typography>
                            </Grid>
                            <Grid item xs={4}>
                              <Typography variant="body2" color="text.secondary">Media</Typography>
                              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                                {listing.photos + listing.videos}
                              </Typography>
                            </Grid>
                          </Grid>

                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton size="small" color="primary">
                              <VisibilityIcon />
                            </IconButton>
                            <IconButton size="small" color="primary" onClick={() => handleOpenDialog('edit-listing', listing)}>
                              <EditIcon />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <DeleteIcon />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {selectedTab === 1 && (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Media Library
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<PhotoCameraIcon />}
                      onClick={() => handleOpenDialog('upload-image')}
                    >
                      Upload Images
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<VideoLibraryIcon />}
                      onClick={() => handleOpenDialog('upload-video')}
                    >
                      Upload Videos
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<YouTubeIcon />}
                      onClick={() => handleOpenDialog('add-youtube')}
                    >
                      Add YouTube
                    </Button>
                  </Box>
                </Box>

                <Grid container spacing={3}>
                  {mediaLibrary.map((media) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={media.id}>
                      <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
                        <CardMedia
                          component="img"
                          height="200"
                          image={media.url}
                          alt={media.title}
                        />
                        <CardContent>
                          <Typography variant="body1" sx={{ fontWeight: 600, mb: 1 }}>
                            {media.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {media.type.toUpperCase()} • {media.uploadDate}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton size="small" color="primary">
                              <EditIcon />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <DeleteIcon />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {selectedTab === 2 && (
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  Page Management
                </Typography>
                <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200', p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                    <FacebookIcon sx={{ color: '#1877f2', fontSize: 40 }} />
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Facebook-like Page Management
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Manage your public page like Facebook
                      </Typography>
                    </Box>
                  </Box>

                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                        Page Information
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Page Name"
                            secondary={user?.name + "'s Ganesh Celebrations"}
                          />
                          <IconButton onClick={() => handleOpenDialog('edit-page')}>
                            <EditIcon />
                          </IconButton>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Page URL"
                            secondary={`/page/${user?.name?.toLowerCase().replace(' ', '-')}`}
                          />
                          <IconButton>
                            <LinkIcon />
                          </IconButton>
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Page Category"
                            secondary="Religious Organization"
                          />
                          <IconButton onClick={() => handleOpenDialog('edit-category')}>
                            <EditIcon />
                          </IconButton>
                        </ListItem>
                      </List>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                        Quick Actions
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Button
                          variant="outlined"
                          startIcon={<EditIcon />}
                          onClick={() => handleOpenDialog('edit-page')}
                          fullWidth
                        >
                          Edit Page Info
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<PhotoCameraIcon />}
                          onClick={() => handleOpenDialog('change-cover')}
                          fullWidth
                        >
                          Change Cover Photo
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<AnalyticsIcon />}
                          fullWidth
                        >
                          View Page Insights
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Card>
              </Box>
            )}

            {selectedTab === 3 && (
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                  Analytics & Insights
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200', p: 3 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                        Performance Overview
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Total Page Views</Typography>
                        <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                          {userStats.totalViews.toLocaleString()}
                        </Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">Engagement Rate</Typography>
                        <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                          7.2%
                        </Typography>
                      </Box>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200', p: 3 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                        Recent Activity
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="New listing created"
                            secondary="2 hours ago"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Photo uploaded"
                            secondary="1 day ago"
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Page viewed 50 times"
                            secondary="2 days ago"
                          />
                        </ListItem>
                      </List>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        </Paper>

        {/* Dialogs */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'listing' && 'Create New Listing'}
            {dialogType === 'edit-listing' && 'Edit Listing'}
            {dialogType === 'upload-image' && 'Upload Images'}
            {dialogType === 'upload-video' && 'Upload Videos'}
            {dialogType === 'add-youtube' && 'Add YouTube Video'}
            {dialogType === 'edit-page' && 'Edit Page Information'}
            {dialogType === 'profile' && 'Profile Settings'}
          </DialogTitle>

          <DialogContent>
            {(dialogType === 'listing' || dialogType === 'edit-listing') && (
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Listing Title"
                      value={formData.title || ''}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      placeholder="e.g., My Home Ganesh 2024"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={formData.category || ''}
                        onChange={(e) => setFormData({...formData, category: e.target.value})}
                        label="Category"
                      >
                        <MenuItem value="Home">Home Ganesh</MenuItem>
                        <MenuItem value="Mandal">Mandal Ganesh</MenuItem>
                        <MenuItem value="Celebrity">Celebrity Ganesh</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Location"
                      value={formData.location || ''}
                      onChange={(e) => setFormData({...formData, location: e.target.value})}
                      placeholder="e.g., Mumbai, Maharashtra"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Description"
                      value={formData.description || ''}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      placeholder="Describe your Ganesh celebration..."
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Contact Phone"
                      value={formData.phone || ''}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      placeholder="+91-XXXXXXXXXX"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Contact Email"
                      value={formData.email || ''}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      placeholder="<EMAIL>"
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {dialogType === 'upload-image' && (
              <Box sx={{ pt: 2, textAlign: 'center' }}>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="image-upload"
                  multiple
                  type="file"
                />
                <label htmlFor="image-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<UploadIcon />}
                    sx={{ mb: 2, p: 4, minHeight: 120 }}
                    fullWidth
                  >
                    <Box>
                      <PhotoCameraIcon sx={{ fontSize: 48, mb: 1 }} />
                      <Typography>Click to upload images</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Supports JPG, PNG, GIF (Max 10MB each)
                      </Typography>
                    </Box>
                  </Button>
                </label>
              </Box>
            )}

            {dialogType === 'upload-video' && (
              <Box sx={{ pt: 2, textAlign: 'center' }}>
                <input
                  accept="video/*"
                  style={{ display: 'none' }}
                  id="video-upload"
                  multiple
                  type="file"
                />
                <label htmlFor="video-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<UploadIcon />}
                    sx={{ mb: 2, p: 4, minHeight: 120 }}
                    fullWidth
                  >
                    <Box>
                      <VideoLibraryIcon sx={{ fontSize: 48, mb: 1 }} />
                      <Typography>Click to upload videos</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Supports MP4, AVI, MOV (Max 100MB each)
                      </Typography>
                    </Box>
                  </Button>
                </label>
              </Box>
            )}

            {dialogType === 'add-youtube' && (
              <Box sx={{ pt: 2 }}>
                <TextField
                  fullWidth
                  label="YouTube Video URL"
                  value={formData.youtubeUrl || ''}
                  onChange={(e) => setFormData({...formData, youtubeUrl: e.target.value})}
                  placeholder="https://www.youtube.com/watch?v=..."
                  sx={{ mb: 3 }}
                />
                <TextField
                  fullWidth
                  label="Video Title"
                  value={formData.videoTitle || ''}
                  onChange={(e) => setFormData({...formData, videoTitle: e.target.value})}
                  placeholder="e.g., Ganesh Aarti 2024"
                  sx={{ mb: 3 }}
                />
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Video Description"
                  value={formData.videoDescription || ''}
                  onChange={(e) => setFormData({...formData, videoDescription: e.target.value})}
                  placeholder="Describe your video..."
                />
              </Box>
            )}

            {dialogType === 'edit-page' && (
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Page Name"
                      value={formData.pageName || user?.name + "'s Ganesh Celebrations"}
                      onChange={(e) => setFormData({...formData, pageName: e.target.value})}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Page Description"
                      value={formData.pageDescription || ''}
                      onChange={(e) => setFormData({...formData, pageDescription: e.target.value})}
                      placeholder="Tell people about your page..."
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Page Category</InputLabel>
                      <Select
                        value={formData.pageCategory || 'Religious Organization'}
                        onChange={(e) => setFormData({...formData, pageCategory: e.target.value})}
                        label="Page Category"
                      >
                        <MenuItem value="Religious Organization">Religious Organization</MenuItem>
                        <MenuItem value="Community">Community</MenuItem>
                        <MenuItem value="Event">Event</MenuItem>
                        <MenuItem value="Public Figure">Public Figure</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Website"
                      value={formData.website || ''}
                      onChange={(e) => setFormData({...formData, website: e.target.value})}
                      placeholder="https://your-website.com"
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>

          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            <Button onClick={handleFormSubmit} variant="contained">
              {dialogType === 'listing' && 'Create Listing'}
              {dialogType === 'edit-listing' && 'Update Listing'}
              {dialogType === 'upload-image' && 'Upload Images'}
              {dialogType === 'upload-video' && 'Upload Videos'}
              {dialogType === 'add-youtube' && 'Add Video'}
              {dialogType === 'edit-page' && 'Save Changes'}
              {dialogType === 'profile' && 'Save Settings'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default Dashboard;
