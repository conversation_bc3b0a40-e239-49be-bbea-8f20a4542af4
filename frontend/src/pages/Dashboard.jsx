import React from 'react';
import { useAuth } from '../context/AuthContext';
import './Dashboard.css';

const Dashboard = () => {
  const { user } = useAuth();

  const renderUserTypeSpecificContent = () => {
    switch (user?.userType) {
      case 'mandal':
        return (
          <div className="mandal-dashboard">
            <div className="plan-info">
              <h3>Current Plan</h3>
              <div className="plan-card">
                <span className="plan-name">
                  {user.mandalInfo?.subscriptionPlan?.toUpperCase() || 'FREE'}
                </span>
                <span className="plan-status">Active</span>
              </div>
            </div>
            
            <div className="mandal-details">
              <h3>Organization Details</h3>
              <div className="details-grid">
                <div className="detail-item">
                  <label>Organization Name:</label>
                  <span>{user.mandalInfo?.organizationName}</span>
                </div>
                <div className="detail-item">
                  <label>Established Year:</label>
                  <span>{user.mandalInfo?.establishedYear}</span>
                </div>
                <div className="detail-item">
                  <label>Registration Number:</label>
                  <span>{user.mandalInfo?.registrationNumber}</span>
                </div>
                <div className="detail-item">
                  <label>President:</label>
                  <span>{user.mandalInfo?.presidentName}</span>
                </div>
                <div className="detail-item">
                  <label>Secretary:</label>
                  <span>{user.mandalInfo?.secretaryName}</span>
                </div>
              </div>
            </div>
          </div>
        );

      case 'celebrity':
        return (
          <div className="celebrity-dashboard">
            <div className="verification-status">
              <h3>Verification Status</h3>
              <div className={`status-badge ${user.celebrityInfo?.verificationStatus}`}>
                {user.celebrityInfo?.verificationStatus?.toUpperCase() || 'PENDING'}
              </div>
            </div>
            
            <div className="celebrity-details">
              <h3>Professional Details</h3>
              <div className="details-grid">
                <div className="detail-item">
                  <label>Profession:</label>
                  <span>{user.celebrityInfo?.profession}</span>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="individual-dashboard">
            <div className="welcome-message">
              <h3>Welcome to your personal Ganesha showcase!</h3>
              <p>Start by uploading photos of your Ganesha celebration.</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="dashboard-page">
      <div className="dashboard-container">
        <div className="dashboard-header">
          <h1>Dashboard</h1>
          <p>Welcome back, {user?.name}!</p>
        </div>

        <div className="dashboard-content">
          {/* User Info Card */}
          <div className="user-info-card">
            <div className="user-avatar">
              {user?.name?.charAt(0).toUpperCase()}
            </div>
            <div className="user-details">
              <h2>{user?.name}</h2>
              <p className="user-type">{user?.userType?.toUpperCase()} Account</p>
              <p className="user-email">{user?.email}</p>
              <p className="user-phone">{user?.phone}</p>
            </div>
          </div>

          {/* Address Info */}
          <div className="address-card">
            <h3>Address</h3>
            <p>{user?.fullAddress}</p>
          </div>

          {/* User Type Specific Content */}
          {renderUserTypeSpecificContent()}

          {/* Quick Actions */}
          <div className="quick-actions">
            <h3>Quick Actions</h3>
            <div className="actions-grid">
              <button className="action-btn">
                📸 Upload Photos
              </button>
              <button className="action-btn">
                📝 Edit Profile
              </button>
              <button className="action-btn">
                📊 View Analytics
              </button>
              <button className="action-btn">
                🎉 Create Event
              </button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="recent-activity">
            <h3>Recent Activity</h3>
            <div className="activity-list">
              <div className="activity-item">
                <span className="activity-icon">✅</span>
                <span className="activity-text">Account created successfully</span>
                <span className="activity-time">Just now</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
