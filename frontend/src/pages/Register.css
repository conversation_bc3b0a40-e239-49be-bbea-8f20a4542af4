/* Register Page Styles */
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  padding: 2rem 1rem;
}

.register-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.register-header {
  text-align: center;
  margin-bottom: 2rem;
}

.register-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.register-header p {
  color: #666;
  font-size: 1rem;
}

.register-form {
  display: flex;
  flex-direction: column;
}

.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.form-section:last-of-type {
  border-bottom: none;
}

.form-section h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.user-type-select {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  font-weight: 500;
}

.mandal-fields,
.celebrity-fields,
.individual-info {
  margin-top: 1rem;
}

.plan-details {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 5px;
  margin-top: 1rem;
}

.plan-details h4 {
  color: #333;
  margin-bottom: 0.5rem;
}

.plan-details ul {
  list-style: none;
  padding: 0;
}

.plan-details li {
  padding: 0.25rem 0;
  color: #555;
}

.info-note {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 5px;
  padding: 1rem;
  margin-top: 1rem;
}

.info-note p {
  margin: 0;
  color: #0066cc;
  font-size: 0.9rem;
}

.register-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 5px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.register-footer {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.register-footer p {
  color: #666;
  font-size: 0.9rem;
}

.register-footer a {
  color: #ff6b35;
  text-decoration: none;
  font-weight: 500;
}

.register-footer a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .register-container {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .register-header h1 {
    font-size: 2rem;
  }
}
