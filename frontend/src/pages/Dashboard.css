/* Dashboard Page Styles */
.dashboard-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 1rem;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.dashboard-header p {
  color: #666;
  font-size: 1.1rem;
}

.dashboard-content {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.user-info-card {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  grid-column: 1 / -1;
}

.user-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  flex-shrink: 0;
}

.user-details h2 {
  color: #333;
  margin-bottom: 0.5rem;
}

.user-type {
  background: #ff6b35;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-block;
  margin-bottom: 0.5rem;
}

.user-email,
.user-phone {
  color: #666;
  margin-bottom: 0.25rem;
}

.address-card,
.mandal-dashboard,
.celebrity-dashboard,
.individual-dashboard,
.quick-actions,
.recent-activity {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.address-card h3,
.mandal-dashboard h3,
.celebrity-dashboard h3,
.individual-dashboard h3,
.quick-actions h3,
.recent-activity h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.plan-info {
  margin-bottom: 1.5rem;
}

.plan-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 5px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-name {
  font-weight: 600;
  color: #ff6b35;
  text-transform: uppercase;
}

.plan-status {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.details-grid {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-weight: 500;
  color: #333;
}

.detail-item span {
  color: #666;
}

.verification-status {
  margin-bottom: 1.5rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-align: center;
  text-transform: uppercase;
  font-size: 0.9rem;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.verified {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.welcome-message {
  text-align: center;
  padding: 2rem;
}

.welcome-message h3 {
  color: #333;
  margin-bottom: 1rem;
}

.welcome-message p {
  color: #666;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.action-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 5px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.action-btn:hover {
  background: #ff6b35;
  color: white;
  border-color: #ff6b35;
  transform: translateY(-2px);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 5px;
}

.activity-icon {
  font-size: 1.2rem;
}

.activity-text {
  flex: 1;
  color: #333;
}

.activity-time {
  color: #666;
  font-size: 0.8rem;
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
  
  .user-info-card {
    flex-direction: column;
    text-align: center;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
