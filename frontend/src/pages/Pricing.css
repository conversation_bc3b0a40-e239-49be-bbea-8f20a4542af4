/* Pricing Page Styles */
.pricing-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 1rem;
}

.pricing-container {
  max-width: 1200px;
  margin: 0 auto;
}

.pricing-header {
  text-align: center;
  margin-bottom: 3rem;
}

.pricing-header h1 {
  font-size: 3rem;
  color: #333;
  margin-bottom: 1rem;
}

.pricing-header p {
  font-size: 1.2rem;
  color: #666;
}

.free-plans,
.mandal-plans {
  margin-bottom: 4rem;
}

.free-plans h2,
.mandal-plans h2 {
  text-align: center;
  font-size: 2rem;
  color: #333;
  margin-bottom: 1rem;
}

.mandal-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
}

.plans-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.plans-grid:not(.mandal-grid) {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.mandal-grid {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.plan-card {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: transform 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-5px);
}

.plan-card.featured {
  border: 3px solid #ff6b35;
  transform: scale(1.02);
}

.plan-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff6b35;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.plan-header h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.plan-price {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: #ff6b35;
}

.period {
  color: #666;
  font-size: 0.9rem;
}

.plan-features h4 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.plan-features p {
  color: #666;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.plan-features ul {
  list-style: none;
  margin-bottom: 1rem;
}

.plan-features li {
  padding: 0.25rem 0;
  color: #555;
  font-size: 0.9rem;
}

.plan-limits {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 5px;
  margin-top: 1rem;
}

.limit-item {
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
  font-size: 0.9rem;
}

.limit-item span:first-child {
  color: #666;
}

.limit-item span:last-child {
  font-weight: 500;
  color: #333;
}

.plan-action {
  margin-top: 1.5rem;
}

.plan-action .btn {
  width: 100%;
}

.comparison-section {
  margin-bottom: 4rem;
}

.comparison-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #333;
  margin-bottom: 2rem;
}

.comparison-table-container {
  overflow-x: auto;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

.comparison-table th,
.comparison-table td {
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.comparison-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.comparison-table td {
  color: #666;
}

.comparison-table tr:hover {
  background: #f8f9fa;
}

.faq-section {
  margin-bottom: 4rem;
}

.faq-section h2 {
  text-align: center;
  font-size: 2rem;
  color: #333;
  margin-bottom: 2rem;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq-item h4 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.faq-item p {
  color: #666;
  line-height: 1.6;
}

.pricing-cta {
  text-align: center;
  background: white;
  padding: 3rem 2rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.pricing-cta h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 1rem;
}

.pricing-cta p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  text-align: center;
}

.error-container p {
  color: #dc3545;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .pricing-header h1 {
    font-size: 2rem;
  }
  
  .plans-grid,
  .mandal-grid {
    grid-template-columns: 1fr;
  }
  
  .plan-card.featured {
    transform: none;
  }
  
  .comparison-table-container {
    margin: 0 -1rem;
  }
  
  .faq-grid {
    grid-template-columns: 1fr;
  }
}
