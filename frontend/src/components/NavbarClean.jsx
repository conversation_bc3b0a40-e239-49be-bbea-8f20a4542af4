import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Container,
  Avatar,
  useTheme,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Menu as MenuIcon,
  Login as LoginIcon,
  PersonAdd as PersonAddIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Navbar = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [userMenuAnchor, setUserMenuAnchor] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleUserMenuOpen = (event) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
    navigate('/');
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  const navigationItems = [
    { path: '/', label: 'Home' },
    { path: '/pricing', label: 'Pricing' },
  ];

  return (
    <>
      <AppBar 
        position="sticky" 
        elevation={0}
        sx={{
          bgcolor: 'white',
          borderBottom: '1px solid',
          borderBottomColor: 'grey.200',
        }}
      >
        <Container maxWidth="xl">
          <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>
            {/* Logo */}
            <Box
              component={Link}
              to="/"
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                color: 'text.primary',
              }}
            >
              <Typography 
                variant="h6" 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  fontWeight: 700,
                  fontSize: '1.5rem'
                }}
              >
                <span style={{ fontSize: '1.8rem', marginRight: '8px' }}>🕉️</span>
                Ganesh Darshan
              </Typography>
            </Box>

            {/* Desktop Navigation */}
            {!isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                {/* Navigation Links */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  {navigationItems.map((item) => (
                    <Button
                      key={item.path}
                      component={Link}
                      to={item.path}
                      sx={{
                        color: 'text.primary',
                        fontWeight: 500,
                        fontSize: '1rem',
                        textTransform: 'none',
                        px: 2,
                        py: 1,
                        borderRadius: 2,
                        '&:hover': {
                          bgcolor: 'grey.100',
                        },
                        backgroundColor: isActive(item.path) ? 'grey.100' : 'transparent',
                      }}
                    >
                      {item.label}
                    </Button>
                  ))}
                </Box>

                {/* Auth Buttons */}
                <Box sx={{ display: 'flex', gap: 2 }}>
                  {isAuthenticated ? (
                    <>
                      <IconButton
                        onClick={handleUserMenuOpen}
                        sx={{
                          p: 0,
                        }}
                      >
                        <Avatar 
                          sx={{ 
                            width: 40, 
                            height: 40, 
                            bgcolor: 'primary.main',
                            fontSize: '1rem'
                          }}
                        >
                          {user?.name?.charAt(0).toUpperCase()}
                        </Avatar>
                      </IconButton>
                      
                      <Menu
                        anchorEl={userMenuAnchor}
                        open={Boolean(userMenuAnchor)}
                        onClose={handleUserMenuClose}
                        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                        sx={{ mt: 1 }}
                      >
                        <MenuItem disabled sx={{ opacity: 1 }}>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {user?.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {user?.email}
                            </Typography>
                          </Box>
                        </MenuItem>
                        <Divider />
                        <MenuItem 
                          component={Link} 
                          to="/dashboard"
                          onClick={handleUserMenuClose}
                        >
                          <AccountCircleIcon sx={{ mr: 2 }} />
                          Dashboard
                        </MenuItem>
                        <MenuItem onClick={handleLogout}>
                          <LogoutIcon sx={{ mr: 2 }} />
                          Logout
                        </MenuItem>
                      </Menu>
                    </>
                  ) : (
                    <>
                      <Button
                        component={Link}
                        to="/login"
                        variant="outlined"
                        startIcon={<LoginIcon />}
                        sx={{
                          color: 'text.primary',
                          borderColor: 'grey.300',
                          textTransform: 'none',
                          fontWeight: 500,
                          '&:hover': {
                            borderColor: 'primary.main',
                            bgcolor: 'rgba(164, 36, 59, 0.04)',
                          },
                        }}
                      >
                        Login
                      </Button>
                      <Button
                        component={Link}
                        to="/register"
                        variant="contained"
                        startIcon={<PersonAddIcon />}
                        sx={{
                          bgcolor: 'primary.main',
                          textTransform: 'none',
                          fontWeight: 500,
                          '&:hover': {
                            bgcolor: 'primary.dark',
                          },
                        }}
                      >
                        Register
                      </Button>
                    </>
                  )}
                </Box>
              </Box>
            )}

            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                onClick={toggleMobileMenu}
                sx={{ 
                  color: 'text.primary',
                }}
              >
                <MenuIcon />
              </IconButton>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={toggleMobileMenu}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            pt: 2,
          },
        }}
      >
        <Box sx={{ px: 3, pb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 700 }}>
            Menu
          </Typography>
        </Box>
        <Divider />
        
        <List sx={{ px: 2 }}>
          {navigationItems.map((item) => (
            <ListItem
              key={item.path}
              component={Link}
              to={item.path}
              onClick={toggleMobileMenu}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&:hover': {
                  bgcolor: 'grey.100',
                },
                backgroundColor: isActive(item.path) ? 'grey.100' : 'transparent',
              }}
            >
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: 500,
                }}
              />
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ px: 3, pb: 3 }}>
          {isAuthenticated ? (
            <Box>
              <Typography variant="body2" sx={{ mb: 2, fontWeight: 600 }}>
                {user?.name}
              </Typography>
              <Button
                component={Link}
                to="/dashboard"
                variant="outlined"
                fullWidth
                sx={{ mb: 2, textTransform: 'none' }}
                onClick={toggleMobileMenu}
              >
                Dashboard
              </Button>
              <Button
                onClick={() => {
                  handleLogout();
                  toggleMobileMenu();
                }}
                variant="outlined"
                fullWidth
                sx={{ textTransform: 'none' }}
              >
                Logout
              </Button>
            </Box>
          ) : (
            <Box>
              <Button
                component={Link}
                to="/login"
                variant="outlined"
                fullWidth
                sx={{ mb: 2, textTransform: 'none' }}
                onClick={toggleMobileMenu}
              >
                Login
              </Button>
              <Button
                component={Link}
                to="/register"
                variant="contained"
                fullWidth
                sx={{ textTransform: 'none' }}
                onClick={toggleMobileMenu}
              >
                Register
              </Button>
            </Box>
          )}
        </Box>
      </Drawer>
    </>
  );
};

export default Navbar;
