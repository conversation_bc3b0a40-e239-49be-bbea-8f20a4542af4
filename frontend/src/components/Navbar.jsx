import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Container,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  Menu as MenuIcon,
  Home as HomeIcon,
  PriceCheck as PriceCheckIcon,
  Dashboard as DashboardIcon,
  Login as LoginIcon,
  PersonAdd as PersonAddIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon,
  Search as SearchIcon,
  Favorite as FavoriteIcon,
  Business as BusinessIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const Navbar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState(null);

  const handleLogout = () => {
    logout();
    navigate('/');
    setUserMenuAnchor(null);
    setMobileMenuOpen(false);
  };

  const handleUserMenuOpen = (event) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  const getUserTypeColor = (userType) => {
    switch (userType) {
      case 'individual': return 'primary';
      case 'mandal': return 'secondary';
      case 'celebrity': return 'warning';
      default: return 'default';
    }
  };

  const navigationItems = [
    { path: '/', label: 'Home', icon: <HomeIcon /> },
    { path: '/mandals', label: 'Mandals', icon: <BusinessIcon /> },
    { path: '/celebrity', label: 'Celebrity Ganesha', icon: <StarIcon /> },
    { path: '/pricing', label: 'Pricing', icon: <PriceCheckIcon /> },
  ];

  if (isAuthenticated) {
    navigationItems.push({ path: '/dashboard', label: 'Dashboard', icon: <DashboardIcon /> });
  }

  const renderMobileMenu = () => (
    <Drawer
      anchor="left"
      open={mobileMenuOpen}
      onClose={toggleMobileMenu}
      sx={{
        '& .MuiDrawer-paper': {
          width: 280,
          background: 'linear-gradient(135deg, #a4243b, #d8973c)',
          color: 'white',
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <span style={{ fontSize: '1.5rem', marginRight: '8px' }}>🕉️</span>
          Live Ganesh
        </Typography>
      </Box>

      <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.2)' }} />

      <List>
        {navigationItems.map((item) => (
          <ListItem
            key={item.path}
            component={Link}
            to={item.path}
            onClick={toggleMobileMenu}
            sx={{
              color: 'white',
              textDecoration: 'none',
              backgroundColor: isActive(item.path) ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItem>
        ))}

        {!isAuthenticated && (
          <>
            <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.2)', my: 1 }} />
            <ListItem
              component={Link}
              to="/login"
              onClick={toggleMobileMenu}
              sx={{
                color: 'white',
                textDecoration: 'none',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
              }}
            >
              <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                <LoginIcon />
              </ListItemIcon>
              <ListItemText primary="Login" />
            </ListItem>
            <ListItem
              component={Link}
              to="/register"
              onClick={toggleMobileMenu}
              sx={{
                color: 'white',
                textDecoration: 'none',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
              }}
            >
              <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                <PersonAddIcon />
              </ListItemIcon>
              <ListItemText primary="Register" />
            </ListItem>
          </>
        )}

        {isAuthenticated && (
          <>
            <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.2)', my: 1 }} />
            <ListItem sx={{ flexDirection: 'column', alignItems: 'flex-start', py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar sx={{ width: 32, height: 32, mr: 1, bgcolor: 'rgba(255, 255, 255, 0.2)' }}>
                  {user?.name?.charAt(0).toUpperCase()}
                </Avatar>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {user?.name}
                </Typography>
              </Box>
              <Chip
                label={user?.userType?.toUpperCase()}
                size="small"
                color={getUserTypeColor(user?.userType)}
                sx={{ mb: 1 }}
              />
            </ListItem>
            <ListItem
              onClick={handleLogout}
              sx={{
                color: 'white',
                cursor: 'pointer',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' },
              }}
            >
              <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </ListItem>
          </>
        )}
      </List>
    </Drawer>
  );

  return (
    <>
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'white',
          borderBottom: '1px solid',
          borderBottomColor: 'neutral.light',
        }}
      >
        <Container maxWidth="xl">
          <Toolbar sx={{ justifyContent: 'space-between', py: 1.5 }}>
            {/* Logo and Navigation */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              {/* Logo */}
              <Box
                component={Link}
                to="/"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  color: 'text.primary',
                }}
              >
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', fontWeight: 700 }}>
                  <span style={{ fontSize: '1.8rem', marginRight: '8px' }}>🕉️</span>
                  Ganesh Darshan
                </Typography>
              </Box>

              {/* Desktop Navigation */}
              {!isMobile && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  {navigationItems.slice(0, -1).map((item) => (
                    <Button
                      key={item.path}
                      component={Link}
                      to={item.path}
                      sx={{
                        color: 'text.primary',
                        fontWeight: 500,
                        fontSize: '0.875rem',
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: 'transparent',
                          color: 'primary.main',
                        },
                        backgroundColor: isActive(item.path) ? 'rgba(164, 36, 59, 0.08)' : 'transparent',
                      }}
                    >
                      {item.label}
                    </Button>
                  ))}
                </Box>
              )}
            </Box>

            {/* Right Side - Search and Actions */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {/* Search Bar */}
              {!isMobile && (
                <TextField
                  placeholder="Search"
                  size="small"
                  sx={{
                    minWidth: 200,
                    '& .MuiOutlinedInput-root': {
                      bgcolor: 'neutral.light',
                      borderRadius: 3,
                      '& fieldset': {
                        border: 'none',
                      },
                      '&:hover fieldset': {
                        border: 'none',
                      },
                      '&.Mui-focused fieldset': {
                        border: '1px solid',
                        borderColor: 'primary.main',
                      },
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: 'text.secondary' }} />
                      </InputAdornment>
                    ),
                  }}
                />
              )}

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                {isAuthenticated ? (
                  <>
                    <IconButton
                      sx={{
                        bgcolor: 'neutral.light',
                        color: 'text.primary',
                        '&:hover': {
                          bgcolor: 'neutral.main',
                        },
                      }}
                    >
                      <FavoriteIcon />
                    </IconButton>

                    <Chip
                      label={user?.userType?.toUpperCase()}
                      size="small"
                      color={getUserTypeColor(user?.userType)}
                      sx={{ mr: 1 }}
                    />

                    <IconButton
                      onClick={handleUserMenuOpen}
                      sx={{
                        bgcolor: 'neutral.light',
                        '&:hover': {
                          bgcolor: 'neutral.main',
                        },
                      }}
                    >
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                        {user?.name?.charAt(0).toUpperCase()}
                      </Avatar>
                    </IconButton>

                    <Menu
                      anchorEl={userMenuAnchor}
                      open={Boolean(userMenuAnchor)}
                      onClose={handleUserMenuClose}
                      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                    >
                      <MenuItem disabled>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {user?.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user?.email}
                          </Typography>
                        </Box>
                      </MenuItem>
                      <Divider />
                      <MenuItem onClick={handleLogout}>
                        <LogoutIcon sx={{ mr: 1 }} />
                        Logout
                      </MenuItem>
                    </Menu>
                  </>
                ) : (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      component={Link}
                      to="/login"
                      variant="outlined"
                      startIcon={<LoginIcon />}
                      sx={{
                        color: 'text.primary',
                        borderColor: 'neutral.main',
                        '&:hover': {
                          borderColor: 'primary.main',
                          bgcolor: 'rgba(164, 36, 59, 0.04)',
                        },
                      }}
                    >
                      Login
                    </Button>
                    <Button
                      component={Link}
                      to="/register"
                      variant="contained"
                      startIcon={<PersonAddIcon />}
                      sx={{
                        background: 'linear-gradient(135deg, #a4243b, #d8973c)',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #7a1b2c, #b8762a)',
                        },
                      }}
                    >
                      Register
                    </Button>
                  </Box>
                )}
              </Box>
            </Box>

            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                onClick={toggleMobileMenu}
                sx={{
                  color: 'text.primary',
                  bgcolor: 'neutral.light',
                  '&:hover': {
                    bgcolor: 'neutral.main',
                  },
                }}
              >
                <MenuIcon />
              </IconButton>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Menu */}
      {isMobile && renderMobileMenu()}
    </>
  );
};

export default Navbar;
