// Pricing plans for different user types
const pricingPlans = {
  individual: {
    plan: 'free',
    price: 0,
    features: [
      'Basic Ganesha listing',
      'Upload up to 5 photos',
      'Basic contact information',
      'Standard support'
    ],
    limitations: {
      maxPhotos: 5,
      maxVideos: 0,
      featuredListing: false,
      prioritySupport: false
    }
  },
  
  celebrity: {
    plan: 'free',
    price: 0,
    features: [
      'Premium Ganesha listing',
      'Upload up to 20 photos',
      'Upload up to 5 videos',
      'Verification badge',
      'Featured listing',
      'Priority support',
      'Social media integration'
    ],
    limitations: {
      maxPhotos: 20,
      maxVideos: 5,
      featuredListing: true,
      prioritySupport: true,
      verificationRequired: true
    }
  },
  
  mandal: {
    free: {
      plan: 'free',
      price: 0,
      duration: 365, // days
      features: [
        'Basic Mandal listing',
        'Upload up to 10 photos',
        'Basic event information',
        'Contact details',
        'Standard support'
      ],
      limitations: {
        maxPhotos: 10,
        maxVideos: 0,
        maxEvents: 1,
        featuredListing: false,
        prioritySupport: false,
        customBranding: false,
        analytics: false
      }
    },
    
    gold: {
      plan: 'gold',
      price: 2999, // INR
      duration: 365, // days
      features: [
        'Enhanced Mandal listing',
        'Upload up to 50 photos',
        'Upload up to 10 videos',
        'Multiple event listings',
        'Basic analytics',
        'Email support',
        'Social media integration'
      ],
      limitations: {
        maxPhotos: 50,
        maxVideos: 10,
        maxEvents: 5,
        featuredListing: false,
        prioritySupport: false,
        customBranding: false,
        analytics: true
      }
    },
    
    platinum: {
      plan: 'platinum',
      price: 4999, // INR
      duration: 365, // days
      features: [
        'Premium Mandal listing',
        'Upload up to 100 photos',
        'Upload up to 25 videos',
        'Unlimited event listings',
        'Featured listing',
        'Advanced analytics',
        'Priority support',
        'Custom branding options',
        'Live streaming support'
      ],
      limitations: {
        maxPhotos: 100,
        maxVideos: 25,
        maxEvents: -1, // unlimited
        featuredListing: true,
        prioritySupport: true,
        customBranding: true,
        analytics: true,
        liveStreaming: true
      }
    },
    
    diamond: {
      plan: 'diamond',
      price: 9999, // INR
      duration: 365, // days
      features: [
        'Premium+ Mandal listing',
        'Unlimited photos',
        'Unlimited videos',
        'Unlimited event listings',
        'Top featured listing',
        'Comprehensive analytics',
        'Dedicated support manager',
        'Full custom branding',
        'Live streaming support',
        'Mobile app integration',
        'API access',
        'White-label options'
      ],
      limitations: {
        maxPhotos: -1, // unlimited
        maxVideos: -1, // unlimited
        maxEvents: -1, // unlimited
        featuredListing: true,
        topFeatured: true,
        prioritySupport: true,
        dedicatedSupport: true,
        customBranding: true,
        analytics: true,
        liveStreaming: true,
        apiAccess: true,
        whiteLabel: true
      }
    }
  }
};

// Helper function to get pricing for a specific user type and plan
const getPricing = (userType, planType = null) => {
  if (userType === 'individual' || userType === 'celebrity') {
    return pricingPlans[userType];
  }
  
  if (userType === 'mandal') {
    if (planType && pricingPlans.mandal[planType]) {
      return pricingPlans.mandal[planType];
    }
    return pricingPlans.mandal; // Return all mandal plans
  }
  
  return null;
};

// Helper function to check if a feature is available for a user
const hasFeature = (userType, planType, feature) => {
  const pricing = getPricing(userType, planType);
  if (!pricing) return false;
  
  if (userType === 'mandal') {
    return pricing.limitations && pricing.limitations[feature] !== undefined 
      ? pricing.limitations[feature] 
      : false;
  }
  
  return pricing.limitations && pricing.limitations[feature] !== undefined 
    ? pricing.limitations[feature] 
    : false;
};

// Helper function to get feature limits
const getFeatureLimit = (userType, planType, feature) => {
  const pricing = getPricing(userType, planType);
  if (!pricing) return 0;
  
  if (userType === 'mandal') {
    return pricing.limitations && pricing.limitations[feature] !== undefined 
      ? pricing.limitations[feature] 
      : 0;
  }
  
  return pricing.limitations && pricing.limitations[feature] !== undefined 
    ? pricing.limitations[feature] 
    : 0;
};

module.exports = {
  pricingPlans,
  getPricing,
  hasFeature,
  getFeatureLimit
};
