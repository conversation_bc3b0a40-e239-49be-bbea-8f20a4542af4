const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email'
    ]
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false // Don't include password in queries by default
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^[0-9]{10}$/, 'Please enter a valid 10-digit phone number']
  },
  
  // User Type
  userType: {
    type: String,
    required: [true, 'User type is required'],
    enum: {
      values: ['individual', 'mandal', 'celebrity'],
      message: 'User type must be either individual, mandal, or celebrity'
    }
  },
  
  // Address Information
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required'],
      trim: true
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true
    },
    pincode: {
      type: String,
      required: [true, 'Pincode is required'],
      match: [/^[0-9]{6}$/, 'Please enter a valid 6-digit pincode']
    }
  },
  
  // Mandal-specific fields
  mandalInfo: {
    organizationName: {
      type: String,
      required: function() { return this.userType === 'mandal'; },
      trim: true
    },
    establishedYear: {
      type: Number,
      required: function() { return this.userType === 'mandal'; },
      min: [1800, 'Established year must be after 1800'],
      max: [new Date().getFullYear(), 'Established year cannot be in the future']
    },
    registrationNumber: {
      type: String,
      required: function() { return this.userType === 'mandal'; },
      trim: true
    },
    presidentName: {
      type: String,
      required: function() { return this.userType === 'mandal'; },
      trim: true
    },
    secretaryName: {
      type: String,
      required: function() { return this.userType === 'mandal'; },
      trim: true
    },
    subscriptionPlan: {
      type: String,
      enum: ['free', 'gold', 'platinum', 'diamond'],
      default: 'free',
      required: function() { return this.userType === 'mandal'; }
    },
    subscriptionExpiry: {
      type: Date,
      default: null
    },
    paymentHistory: [{
      planType: {
        type: String,
        enum: ['free', 'gold', 'platinum', 'diamond']
      },
      amount: {
        type: Number,
        default: 0
      },
      paymentDate: {
        type: Date,
        default: Date.now
      },
      transactionId: String,
      status: {
        type: String,
        enum: ['pending', 'completed', 'failed'],
        default: 'pending'
      }
    }]
  },
  
  // Celebrity-specific fields
  celebrityInfo: {
    profession: {
      type: String,
      required: function() { return this.userType === 'celebrity'; },
      trim: true
    },
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'rejected'],
      default: 'pending'
    },
    socialMediaHandles: {
      instagram: String,
      twitter: String,
      facebook: String
    }
  },
  
  // Account Status
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  
  // Password Reset
  passwordResetToken: String,
  passwordResetExpires: Date,
  
  // Profile Image
  profileImage: {
    type: String,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index for better query performance
// Note: email index is already created by unique: true
userSchema.index({ userType: 1 });
userSchema.index({ 'address.city': 1 });

// Virtual for full address
userSchema.virtual('fullAddress').get(function() {
  return `${this.address.street}, ${this.address.city}, ${this.address.state} - ${this.address.pincode}`;
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();
  
  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Instance method to get public profile
userSchema.methods.getPublicProfile = function() {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.emailVerificationToken;
  delete userObject.emailVerificationExpires;
  delete userObject.passwordResetToken;
  delete userObject.passwordResetExpires;
  return userObject;
};

module.exports = mongoose.model('User', userSchema);
