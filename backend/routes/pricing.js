const express = require('express');
const { getPricing, pricingPlans } = require('../config/pricing');

const router = express.Router();

// @route   GET /api/pricing
// @desc    Get all pricing plans
// @access  Public
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        pricing: pricingPlans
      }
    });
  } catch (error) {
    console.error('Get pricing error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @route   GET /api/pricing/:userType
// @desc    Get pricing for specific user type
// @access  Public
router.get('/:userType', (req, res) => {
  try {
    const { userType } = req.params;
    const { plan } = req.query;
    
    if (!['individual', 'mandal', 'celebrity'].includes(userType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user type. Must be individual, mandal, or celebrity'
      });
    }
    
    const pricing = getPricing(userType, plan);
    
    if (!pricing) {
      return res.status(404).json({
        success: false,
        message: 'Pricing not found for the specified user type and plan'
      });
    }
    
    res.json({
      success: true,
      data: {
        userType,
        plan: plan || 'all',
        pricing
      }
    });
  } catch (error) {
    console.error('Get user type pricing error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @route   GET /api/pricing/mandal/compare
// @desc    Get comparison of all mandal plans
// @access  Public
router.get('/mandal/compare', (req, res) => {
  try {
    const mandalPlans = pricingPlans.mandal;
    
    // Create comparison structure
    const comparison = {
      plans: Object.keys(mandalPlans),
      features: {},
      pricing: {}
    };
    
    // Extract all unique features
    const allFeatures = new Set();
    Object.values(mandalPlans).forEach(plan => {
      plan.features.forEach(feature => allFeatures.add(feature));
      Object.keys(plan.limitations).forEach(limitation => allFeatures.add(limitation));
    });
    
    // Build comparison matrix
    allFeatures.forEach(feature => {
      comparison.features[feature] = {};
      Object.keys(mandalPlans).forEach(planKey => {
        const plan = mandalPlans[planKey];
        if (plan.features.includes(feature)) {
          comparison.features[feature][planKey] = true;
        } else if (plan.limitations[feature] !== undefined) {
          comparison.features[feature][planKey] = plan.limitations[feature];
        } else {
          comparison.features[feature][planKey] = false;
        }
      });
    });
    
    // Add pricing information
    Object.keys(mandalPlans).forEach(planKey => {
      const plan = mandalPlans[planKey];
      comparison.pricing[planKey] = {
        price: plan.price,
        duration: plan.duration,
        plan: plan.plan
      };
    });
    
    res.json({
      success: true,
      data: {
        comparison,
        plans: mandalPlans
      }
    });
  } catch (error) {
    console.error('Get mandal comparison error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
