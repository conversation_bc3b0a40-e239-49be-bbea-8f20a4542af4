{"name": "live-ganesh-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ganesh", "<PERSON><PERSON>han", "festival", "mandal"], "author": "", "license": "ISC", "description": "Backend API for Live Ganesh Darshan website", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0"}, "devDependencies": {"nodemon": "^3.1.10"}}